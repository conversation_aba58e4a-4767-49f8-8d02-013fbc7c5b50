<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="abc_decor_view_status_guard">#ff000000</color>
    <color name="abc_decor_view_status_guard_light">#ffffffff</color>
    <color name="abc_search_url_text_normal">#ff7fa87f</color>
    <color name="abc_search_url_text_pressed">@android:color/black</color>
    <color name="abc_search_url_text_selected">@android:color/black</color>
    <color name="accent_material_dark">@color/material_deep_teal_200</color>
    <color name="accent_material_light">@color/material_deep_teal_500</color>
    <color name="androidx_core_ripple_material_light">#1f000000</color>
    <color name="androidx_core_secondary_text_default_material_light">#8a000000</color>
    <color name="background_floating_material_dark">@color/material_grey_800</color>
    <color name="background_floating_material_light">@android:color/white</color>
    <color name="background_material_dark">@color/material_grey_850</color>
    <color name="background_material_light">@color/material_grey_50</color>
    <color name="black">#ff333333</color>
    <color name="bright_foreground_disabled_material_dark">#80ffffff</color>
    <color name="bright_foreground_disabled_material_light">#80000000</color>
    <color name="bright_foreground_material_dark">@android:color/white</color>
    <color name="bright_foreground_material_light">@android:color/black</color>
    <color name="browser_actions_bg_grey">#fff5f5f5</color>
    <color name="browser_actions_divider_color">#1e000000</color>
    <color name="browser_actions_text_color">#de000000</color>
    <color name="browser_actions_title_color">#ff646464</color>
    <color name="button_material_dark">#ff5a595b</color>
    <color name="button_material_light">#ffd6d7d7</color>
    <color name="call_notification_answer_color">#ff1d873b</color>
    <color name="call_notification_decline_color">#ffd93025</color>
    <color name="cardview_dark_background">#ff424242</color>
    <color name="cardview_light_background">#ffffffff</color>
    <color name="cardview_shadow_end_color">#03000000</color>
    <color name="cardview_shadow_start_color">#37000000</color>
    <color name="colorAccent">#ff03dac5</color>
    <color name="colorPrimary">#ffffffff</color>
    <color name="colorPrimaryDark">#ffffffff</color>
    <color name="colorText">#ffa2a2a2</color>
    <color name="colorWhite">#ffffffff</color>
    <color name="common_google_signin_btn_text_dark_default">@android:color/white</color>
    <color name="common_google_signin_btn_text_dark_disabled">#1f000000</color>
    <color name="common_google_signin_btn_text_dark_focused">@android:color/black</color>
    <color name="common_google_signin_btn_text_dark_pressed">@android:color/white</color>
    <color name="common_google_signin_btn_text_light_default">#90000000</color>
    <color name="common_google_signin_btn_text_light_disabled">#1f000000</color>
    <color name="common_google_signin_btn_text_light_focused">#90000000</color>
    <color name="common_google_signin_btn_text_light_pressed">#de000000</color>
    <color name="date_time_color">#ff009688</color>
    <color name="design_dark_default_color_background">#ff121212</color>
    <color name="design_dark_default_color_error">#ffcf6679</color>
    <color name="design_dark_default_color_on_background">#ffffffff</color>
    <color name="design_dark_default_color_on_error">#ff000000</color>
    <color name="design_dark_default_color_on_primary">#ff000000</color>
    <color name="design_dark_default_color_on_secondary">#ff000000</color>
    <color name="design_dark_default_color_on_surface">#ffffffff</color>
    <color name="design_dark_default_color_primary">#ffba86fc</color>
    <color name="design_dark_default_color_primary_dark">#ff000000</color>
    <color name="design_dark_default_color_primary_variant">#ff3700b3</color>
    <color name="design_dark_default_color_secondary">#ff03dac6</color>
    <color name="design_dark_default_color_secondary_variant">#ff03dac6</color>
    <color name="design_dark_default_color_surface">#ff121212</color>
    <color name="design_default_color_background">#ffffffff</color>
    <color name="design_default_color_error">#ffb00020</color>
    <color name="design_default_color_on_background">#ff000000</color>
    <color name="design_default_color_on_error">#ffffffff</color>
    <color name="design_default_color_on_primary">#ffffffff</color>
    <color name="design_default_color_on_secondary">#ff000000</color>
    <color name="design_default_color_on_surface">#ff000000</color>
    <color name="design_default_color_primary">#ff6200ee</color>
    <color name="design_default_color_primary_dark">#ff3700b3</color>
    <color name="design_default_color_primary_variant">#ff3700b3</color>
    <color name="design_default_color_secondary">#ff03dac6</color>
    <color name="design_default_color_secondary_variant">#ff018786</color>
    <color name="design_default_color_surface">#ffffffff</color>
    <color name="design_snackbar_background_color">#ff323232</color>
    <color name="error_color_material_dark">#ffff7043</color>
    <color name="error_color_material_light">#ffff5722</color>
    <color name="foreground_material_dark">@android:color/white</color>
    <color name="foreground_material_light">@android:color/black</color>
    <color name="highlighted_text_material_dark">#6680cbc4</color>
    <color name="highlighted_text_material_light">#66009688</color>
    <color name="link_blue">#ff0645ad</color>
    <color name="material_deep_teal_200">#ff80cbc4</color>
    <color name="material_deep_teal_500">#ff008577</color>
    <color name="material_grey_100">#fff5f5f5</color>
    <color name="material_grey_50">#fffafafa</color>
    <color name="material_grey_600">#ff757575</color>
    <color name="material_grey_800">#ff424242</color>
    <color name="material_grey_850">#ff303030</color>
    <color name="material_grey_900">#ff212121</color>
    <color name="mtrl_scrim_color">#52000000</color>
    <color name="mtrl_textinput_default_box_stroke_color">#6b000000</color>
    <color name="mtrl_textinput_disabled_color">#1f000000</color>
    <color name="mtrl_textinput_focused_box_stroke_color">#00000000</color>
    <color name="mtrl_textinput_hovered_box_stroke_color">#de000000</color>
    <color name="notification_action_color_filter">@color/androidx_core_secondary_text_default_material_light</color>
    <color name="notification_icon_bg_color">#ff9e9e9e</color>
    <color name="primary_dark_material_dark">@android:color/black</color>
    <color name="primary_dark_material_light">@color/material_grey_600</color>
    <color name="primary_material_dark">@color/material_grey_900</color>
    <color name="primary_material_light">@color/material_grey_100</color>
    <color name="primary_text_default_material_dark">#ffffffff</color>
    <color name="primary_text_default_material_light">#de000000</color>
    <color name="primary_text_disabled_material_dark">#4dffffff</color>
    <color name="primary_text_disabled_material_light">#39000000</color>
    <color name="ripple_material_dark">#33ffffff</color>
    <color name="ripple_material_light">#1f000000</color>
    <color name="secondary_text_default_material_dark">#b3ffffff</color>
    <color name="secondary_text_default_material_light">#8a000000</color>
    <color name="secondary_text_disabled_material_dark">#36ffffff</color>
    <color name="secondary_text_disabled_material_light">#24000000</color>
    <color name="splash_bg_color">#ff004014</color>
    <color name="statusBarColor">#ffffffff</color>
    <color name="switch_thumb_disabled_material_dark">#ff616161</color>
    <color name="switch_thumb_disabled_material_light">#ffbdbdbd</color>
    <color name="switch_thumb_normal_material_dark">#ffbdbdbd</color>
    <color name="switch_thumb_normal_material_light">#fff1f1f1</color>
    <color name="tooltip_background_dark">#e6616161</color>
    <color name="tooltip_background_light">#e6ffffff</color>
</resources>
