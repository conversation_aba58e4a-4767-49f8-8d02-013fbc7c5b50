<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="abc_action_bar_home_description">Navigate home</string>
    <string name="abc_action_bar_up_description">Navigate up</string>
    <string name="abc_action_menu_overflow_description">More options</string>
    <string name="abc_action_mode_done">Done</string>
    <string name="abc_activity_chooser_view_see_all">See all</string>
    <string name="abc_activitychooserview_choose_application">Choose an app</string>
    <string name="abc_capital_off">OFF</string>
    <string name="abc_capital_on">ON</string>
    <string name="abc_menu_alt_shortcut_label">Alt+</string>
    <string name="abc_menu_ctrl_shortcut_label">Ctrl+</string>
    <string name="abc_menu_delete_shortcut_label">delete</string>
    <string name="abc_menu_enter_shortcut_label">enter</string>
    <string name="abc_menu_function_shortcut_label">Function+</string>
    <string name="abc_menu_meta_shortcut_label">Meta+</string>
    <string name="abc_menu_shift_shortcut_label">Shift+</string>
    <string name="abc_menu_space_shortcut_label">space</string>
    <string name="abc_menu_sym_shortcut_label">Sym+</string>
    <string name="abc_prepend_shortcut_label">Menu+</string>
    <string name="abc_search_hint">Search…</string>
    <string name="abc_searchview_description_clear">Clear query</string>
    <string name="abc_searchview_description_query">Search query</string>
    <string name="abc_searchview_description_search">Search</string>
    <string name="abc_searchview_description_submit">Submit query</string>
    <string name="abc_searchview_description_voice">Voice search</string>
    <string name="abc_shareactionprovider_share_with">Share with</string>
    <string name="abc_shareactionprovider_share_with_application">Share with %s</string>
    <string name="abc_toolbar_collapse_description">Collapse</string>
    <string name="androidx_startup">androidx.startup</string>
    <string name="app_name">Web2Native</string>
    <string name="appbar_scrolling_view_behavior">scroll</string>
    <string name="bottom_sheet_behavior">com.google.android.material.bottomsheet.BottomSheetBehavior</string>
    <string name="bottomsheet_action_collapse">Collapse the bottom sheet</string>
    <string name="bottomsheet_action_expand">Expand the bottom sheet</string>
    <string name="bottomsheet_action_expand_halfway">Expand halfway</string>
    <string name="bottomsheet_drag_handle_clicked">Drag handle double-tapped</string>
    <string name="bottomsheet_drag_handle_content_description">Drag handle</string>
    <string name="call_notification_answer_action">Answer</string>
    <string name="call_notification_answer_video_action">Video</string>
    <string name="call_notification_decline_action">Decline</string>
    <string name="call_notification_hang_up_action">Hang Up</string>
    <string name="call_notification_incoming_text">Incoming call</string>
    <string name="call_notification_ongoing_text">Ongoing call</string>
    <string name="call_notification_screening_text">Screening an incoming call</string>
    <string name="character_counter_content_description">Characters entered %1$d of %2$d</string>
    <string name="character_counter_overflowed_content_description">Character limit exceeded %1$d of %2$d</string>
    <string name="character_counter_pattern">%1$d/%2$d</string>
    <string name="clear_text_end_icon_content_description">Clear text</string>
    <string name="close_drawer">Close navigation menu</string>
    <string name="close_sheet">Close sheet</string>
    <string name="common_google_play_services_enable_button">Enable</string>
    <string name="common_google_play_services_enable_text">"%1$s won't work unless you enable Google Play services."</string>
    <string name="common_google_play_services_enable_title">Enable Google Play services</string>
    <string name="common_google_play_services_install_button">Install</string>
    <string name="common_google_play_services_install_text">"%1$s won't run without Google Play services, which are missing from your device."</string>
    <string name="common_google_play_services_install_title">Get Google Play services</string>
    <string name="common_google_play_services_notification_channel_name">Google Play services availability</string>
    <string name="common_google_play_services_notification_ticker">Google Play services error</string>
    <string name="common_google_play_services_unknown_issue">%1$s is having trouble with Google Play services. Please try again.</string>
    <string name="common_google_play_services_unsupported_text">"%1$s won't run without Google Play services, which are not supported by your device."</string>
    <string name="common_google_play_services_update_button">Update</string>
    <string name="common_google_play_services_update_text">"%1$s won't run unless you update Google Play services."</string>
    <string name="common_google_play_services_update_title">Update Google Play services</string>
    <string name="common_google_play_services_updating_text">"%1$s won't run without Google Play services, which are currently updating."</string>
    <string name="common_google_play_services_wear_update_text">New version of Google Play services needed. It will update itself shortly.</string>
    <string name="common_open_on_phone">Open on phone</string>
    <string name="common_signin_button_text">Sign in</string>
    <string name="common_signin_button_text_long">Sign in with Google</string>
    <string name="copy_toast_msg">Link copied to clipboard</string>
    <string name="default_error_message">Invalid input</string>
    <string name="default_popup_window_title">Pop-Up Window</string>
    <string name="default_web_client_id">628244457299-e9dnm732j5crtgo3a4v1bvth6sod3l1f.apps.googleusercontent.com</string>
    <string name="dropdown_menu">Dropdown menu</string>
    <string name="error_a11y_label">Error: invalid</string>
    <string name="error_icon_content_description">Error</string>
    <string name="exposed_dropdown_menu_content_description">Show dropdown menu</string>
    <string name="fab_transformation_scrim_behavior">com.google.android.material.transformation.FabTransformationScrimBehavior</string>
    <string name="fab_transformation_sheet_behavior">com.google.android.material.transformation.FabTransformationSheetBehavior</string>
    <string name="facebook_app_id">722156285706830</string>
    <string name="facebook_client_token">********************************</string>
    <string name="fcm_fallback_notification_channel_label">Miscellaneous</string>
    <string name="firebase_database_url">https://alpha-af0d2.firebaseio.com</string>
    <string name="fontawesome_version">*******-kotlin</string>
    <string name="gcm_defaultSenderId">628244457299</string>
    <string name="google_api_key">AIzaSyBqQYtN5NJDttym4Gho7PAu0xvF5YvCToA</string>
    <string name="google_app_id">1:628244457299:android:bac7f2c07691ec756323a6</string>
    <string name="google_crash_reporting_api_key">AIzaSyBqQYtN5NJDttym4Gho7PAu0xvF5YvCToA</string>
    <string name="google_storage_bucket">alpha-af0d2.appspot.com</string>
    <string name="icon_content_description">Dialog Icon</string>
    <string name="iconics_lib_version">5.4.0</string>
    <string name="iconics_typeface_api_version">5.4.0</string>
    <string name="in_progress">In progress</string>
    <string name="indeterminate">Partially checked</string>
    <string name="item_view_role_description">Tab</string>
    <string name="location_permission_missing_message">"Looks like this app doesn't have location services configured. Please see OneSignal docs for more information."</string>
    <string name="location_permission_missing_title">App Missing Permission</string>
    <string name="location_permission_name_for_title">Location</string>
    <string name="location_permission_settings_message">sharing your device location</string>
    <string name="m3_exceed_max_badge_text_suffix">%1$s%2$s</string>
    <string name="m3_ref_typeface_brand_regular">sans-serif</string>
    <string name="m3_ref_typeface_plain_medium">sans-serif-medium</string>
    <string name="m3_ref_typeface_plain_regular">sans-serif</string>
    <string name="m3c_bottom_sheet_collapse_description">Collapse bottom sheet</string>
    <string name="m3c_bottom_sheet_dismiss_description">Dismiss bottom sheet</string>
    <string name="m3c_bottom_sheet_drag_handle_description">Drag handle</string>
    <string name="m3c_bottom_sheet_expand_description">Expand bottom sheet</string>
    <string name="m3c_bottom_sheet_pane_title">Bottom Sheet</string>
    <string name="material_clock_display_divider">:</string>
    <string name="material_clock_toggle_content_description">Select AM or PM</string>
    <string name="material_motion_easing_accelerated">cubic-bezier(0.4, 0.0, 1.0, 1.0)</string>
    <string name="material_motion_easing_decelerated">cubic-bezier(0.0, 0.0, 0.2, 1.0)</string>
    <string name="material_motion_easing_emphasized">path(M 0,0 C 0.05, 0, 0.133333, 0.06, 0.166666, 0.4 C 0.208333, 0.82, 0.25, 1, 1, 1)</string>
    <string name="material_motion_easing_linear">cubic-bezier(0.0, 0.0, 1.0, 1.0)</string>
    <string name="material_motion_easing_standard">cubic-bezier(0.4, 0.0, 0.2, 1.0)</string>
    <string name="material_timepicker_am">AM</string>
    <string name="material_timepicker_pm">PM</string>
    <string name="mtrl_badge_numberless_content_description">New notification</string>
    <string name="mtrl_checkbox_button_icon_path_checked">M14,18.2 11.4,15.6 10,17 14,21 22,13 20.6,11.6z</string>
    <string name="mtrl_checkbox_button_icon_path_group_name">icon</string>
    <string name="mtrl_checkbox_button_icon_path_indeterminate">M13.4,15 11,15 11,17 13.4,17 21,17 21,15z</string>
    <string name="mtrl_checkbox_button_icon_path_name">icon path</string>
    <string name="mtrl_checkbox_button_path_checked">M23,7H9C7.9,7,7,7.9,7,9v14c0,1.1,0.9,2,2,2h14c1.1,0,2-0.9,2-2V9C25,7.9,24.1,7,23,7z</string>
    <string name="mtrl_checkbox_button_path_group_name">button</string>
    <string name="mtrl_checkbox_button_path_name">button path</string>
    <string name="mtrl_checkbox_button_path_unchecked">M23,7H9C7.9,7,7,7.9,7,9v14c0,1.1,0.9,2,2,2h14c1.1,0,2-0.9,2-2V9C25,7.9,24.1,7,23,7z M23,23H9V9h14V23z</string>
    <string name="mtrl_checkbox_state_description_checked">Checked</string>
    <string name="mtrl_checkbox_state_description_indeterminate">Partially checked</string>
    <string name="mtrl_checkbox_state_description_unchecked">Not checked</string>
    <string name="mtrl_chip_close_icon_content_description">Remove %1$s</string>
    <string name="mtrl_exceed_max_badge_number_content_description">More than %1$d new notifications</string>
    <string name="mtrl_exceed_max_badge_number_suffix">%1$d%2$s</string>
    <string name="mtrl_picker_a11y_next_month">Change to next month</string>
    <string name="mtrl_picker_a11y_prev_month">Change to previous month</string>
    <string name="mtrl_picker_cancel">Cancel</string>
    <string name="mtrl_picker_confirm">OK</string>
    <string name="mtrl_picker_day_of_week_column_header">%1$s</string>
    <string name="mtrl_picker_navigate_to_current_year_description">Navigate to current year %1$d</string>
    <string name="mtrl_picker_navigate_to_year_description">Navigate to year %1$d</string>
    <string name="mtrl_picker_save">Save</string>
    <string name="mtrl_picker_toggle_to_calendar_input_mode">Switch to calendar input mode</string>
    <string name="mtrl_picker_toggle_to_day_selection">Tap to switch to Calendar view</string>
    <string name="mtrl_picker_toggle_to_text_input_mode">Switch to text input mode</string>
    <string name="mtrl_picker_toggle_to_year_selection">Tap to switch to year view</string>
    <string name="navigation_menu">Navigation menu</string>
    <string name="not_selected">Not selected</string>
    <string name="notification_permission_name_for_title">Notifications</string>
    <string name="notification_permission_settings_message">Notifications</string>
    <string name="orufy_connect_client_id">jVY6FSPTypeVnrDKkcGU4R1bBGsg5SXR</string>
    <string name="password_toggle_content_description">Show password</string>
    <string name="path_password_eye">M12,4.5C7,4.5 2.73,7.61 1,12c1.73,4.39 6,7.5 11,7.5s9.27,-3.11 11,-7.5c-1.73,-4.39 -6,-7.5 -11,-7.5zM12,17c-2.76,0 -5,-2.24 -5,-5s2.24,-5 5,-5 5,2.24 5,5 -2.24,5 -5,5zM12,9c-1.66,0 -3,1.34 -3,3s1.34,3 3,3 3,-1.34 3,-3 -1.34,-3 -3,-3z</string>
    <string name="path_password_eye_mask_strike_through">M2,4.27 L19.73,22 L22.27,19.46 L4.54,1.73 L4.54,1 L23,1 L23,23 L1,23 L1,4.27 Z</string>
    <string name="path_password_eye_mask_visible">M2,4.27 L2,4.27 L4.54,1.73 L4.54,1.73 L4.54,1 L23,1 L23,23 L1,23 L1,4.27 Z</string>
    <string name="path_password_strike_through">M3.27,4.27 L19.74,20.74</string>
    <string name="permission_not_available_message">You have previously denied %s. Please go to settings to enable.</string>
    <string name="permission_not_available_open_settings_option">Settings</string>
    <string name="permission_not_available_title">%s Not Available</string>
    <string name="project_id">alpha-af0d2</string>
    <string name="range_end">Range end</string>
    <string name="range_start">Range start</string>
    <string name="received_message">Received Message</string>
    <string name="search_menu_title">Search</string>
    <string name="searchbar_scrolling_view_behavior">com.google.android.material.search.SearchBar$ScrollingViewBehavior</string>
    <string name="searchview_clear_text_content_description">Clear text</string>
    <string name="searchview_navigation_content_description">Back</string>
    <string name="selected">Selected</string>
    <string name="send_otp">Send OTP</string>
    <string name="side_sheet_accessibility_pane_title">Side Sheet</string>
    <string name="state_off">Off</string>
    <string name="state_on">On</string>
    <string name="status_bar_notification_info_overflow">999+</string>
    <string name="switch_role">Switch</string>
    <string name="tab">Tab</string>
    <string name="template_percent">%1$d percent.</string>
    <string name="title_activity_hello">Hello</string>
    <string name="tooltip_description">tooltip</string>
    <string name="tooltip_label">show tooltip</string>
</resources>
