<?xml version="1.0" encoding="utf-8"?>
<ScrollView android:id="@id/buttonPanel" android:layout_width="fill_parent" android:layout_height="wrap_content" android:fillViewport="true" android:scrollIndicators="top|bottom" style="?buttonBarStyle"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <androidx.appcompat.widget.ButtonBarLayout android:gravity="bottom" android:orientation="horizontal" android:paddingLeft="8.0dip" android:paddingTop="2.0dip" android:paddingRight="8.0dip" android:paddingBottom="2.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layoutDirection="locale" android:paddingStart="8.0dip" android:paddingEnd="8.0dip" android:paddingHorizontal="8.0dip" android:paddingVertical="2.0dip">
        <Button android:id="@android:id/button3" android:layout_width="wrap_content" android:layout_height="wrap_content" style="?buttonBarNeutralButtonStyle" />
        <android.widget.Space android:id="@id/spacer" android:visibility="?materialAlertDialogButtonSpacerVisibility" android:layout_width="0.0dip" android:layout_height="0.0dip" android:layout_weight="1.0" />
        <Button android:id="@android:id/button2" android:layout_width="wrap_content" android:layout_height="wrap_content" style="?buttonBarNegativeButtonStyle" />
        <Button android:id="@android:id/button1" android:layout_width="wrap_content" android:layout_height="wrap_content" style="?buttonBarPositiveButtonStyle" />
    </androidx.appcompat.widget.ButtonBarLayout>
</ScrollView>
