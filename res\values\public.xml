<?xml version="1.0" encoding="utf-8"?>
<resources>
    <public type="anim" name="abc_fade_in" id="0x7f010000" />
    <public type="anim" name="abc_fade_out" id="0x7f010001" />
    <public type="anim" name="abc_grow_fade_in_from_bottom" id="0x7f010002" />
    <public type="anim" name="abc_popup_enter" id="0x7f010003" />
    <public type="anim" name="abc_popup_exit" id="0x7f010004" />
    <public type="anim" name="abc_shrink_fade_out_from_bottom" id="0x7f010005" />
    <public type="anim" name="abc_slide_in_bottom" id="0x7f010006" />
    <public type="anim" name="abc_slide_in_top" id="0x7f010007" />
    <public type="anim" name="abc_slide_out_bottom" id="0x7f010008" />
    <public type="anim" name="abc_slide_out_top" id="0x7f010009" />
    <public type="anim" name="abc_tooltip_enter" id="0x7f01000a" />
    <public type="anim" name="abc_tooltip_exit" id="0x7f01000b" />
    <public type="anim" name="btn_checkbox_to_checked_box_inner_merged_animation" id="0x7f01000c" />
    <public type="anim" name="btn_checkbox_to_checked_box_outer_merged_animation" id="0x7f01000d" />
    <public type="anim" name="btn_checkbox_to_checked_icon_null_animation" id="0x7f01000e" />
    <public type="anim" name="btn_checkbox_to_unchecked_box_inner_merged_animation" id="0x7f01000f" />
    <public type="anim" name="btn_checkbox_to_unchecked_check_path_merged_animation" id="0x7f010010" />
    <public type="anim" name="btn_checkbox_to_unchecked_icon_null_animation" id="0x7f010011" />
    <public type="anim" name="btn_radio_to_off_mtrl_dot_group_animation" id="0x7f010012" />
    <public type="anim" name="btn_radio_to_off_mtrl_ring_outer_animation" id="0x7f010013" />
    <public type="anim" name="btn_radio_to_off_mtrl_ring_outer_path_animation" id="0x7f010014" />
    <public type="anim" name="btn_radio_to_on_mtrl_dot_group_animation" id="0x7f010015" />
    <public type="anim" name="btn_radio_to_on_mtrl_ring_outer_animation" id="0x7f010016" />
    <public type="anim" name="btn_radio_to_on_mtrl_ring_outer_path_animation" id="0x7f010017" />
    <public type="anim" name="fast_fade_out" id="0x7f01001c" />
    <public type="anim" name="fragment_fast_out_extra_slow_in" id="0x7f01001d" />
    <public type="anim" name="linear_indeterminate_line1_head_interpolator" id="0x7f01001e" />
    <public type="anim" name="linear_indeterminate_line1_tail_interpolator" id="0x7f01001f" />
    <public type="anim" name="linear_indeterminate_line2_head_interpolator" id="0x7f010020" />
    <public type="anim" name="linear_indeterminate_line2_tail_interpolator" id="0x7f010021" />
    <public type="anim" name="mtrl_bottom_sheet_slide_in" id="0x7f01002a" />
    <public type="anim" name="mtrl_bottom_sheet_slide_out" id="0x7f01002b" />
    <public type="anim" name="mtrl_card_lowers_interpolator" id="0x7f01002c" />
    <public type="anim" name="onesignal_fade_in" id="0x7f01002d" />
    <public type="anim" name="onesignal_fade_out" id="0x7f01002e" />
    <public type="animator" name="design_appbar_state_list_animator" id="0x7f020000" />
    <public type="animator" name="design_fab_hide_motion_spec" id="0x7f020001" />
    <public type="animator" name="design_fab_show_motion_spec" id="0x7f020002" />
    <public type="animator" name="fragment_close_enter" id="0x7f020003" />
    <public type="animator" name="fragment_close_exit" id="0x7f020004" />
    <public type="animator" name="fragment_fade_enter" id="0x7f020005" />
    <public type="animator" name="fragment_fade_exit" id="0x7f020006" />
    <public type="animator" name="fragment_open_enter" id="0x7f020007" />
    <public type="animator" name="fragment_open_exit" id="0x7f020008" />
    <public type="animator" name="mtrl_btn_state_list_anim" id="0x7f020015" />
    <public type="animator" name="mtrl_btn_unelevated_state_list_anim" id="0x7f020016" />
    <public type="animator" name="mtrl_card_state_list_anim" id="0x7f020017" />
    <public type="animator" name="mtrl_chip_state_list_anim" id="0x7f020018" />
    <public type="animator" name="mtrl_extended_fab_state_list_animator" id="0x7f02001d" />
    <public type="animator" name="mtrl_fab_hide_motion_spec" id="0x7f02001e" />
    <public type="animator" name="mtrl_fab_show_motion_spec" id="0x7f02001f" />
    <public type="attr" name="SharedValue" id="0x7f030000" />
    <public type="attr" name="SharedValueId" id="0x7f030001" />
    <public type="attr" name="action" id="0x7f030002" />
    <public type="attr" name="actionBarDivider" id="0x7f030003" />
    <public type="attr" name="actionBarItemBackground" id="0x7f030004" />
    <public type="attr" name="actionBarPopupTheme" id="0x7f030005" />
    <public type="attr" name="actionBarSize" id="0x7f030006" />
    <public type="attr" name="actionBarSplitStyle" id="0x7f030007" />
    <public type="attr" name="actionBarStyle" id="0x7f030008" />
    <public type="attr" name="actionBarTabBarStyle" id="0x7f030009" />
    <public type="attr" name="actionBarTabStyle" id="0x7f03000a" />
    <public type="attr" name="actionBarTabTextStyle" id="0x7f03000b" />
    <public type="attr" name="actionBarTheme" id="0x7f03000c" />
    <public type="attr" name="actionBarWidgetTheme" id="0x7f03000d" />
    <public type="attr" name="actionButtonStyle" id="0x7f03000e" />
    <public type="attr" name="actionDropDownStyle" id="0x7f03000f" />
    <public type="attr" name="actionLayout" id="0x7f030010" />
    <public type="attr" name="actionMenuTextAppearance" id="0x7f030011" />
    <public type="attr" name="actionMenuTextColor" id="0x7f030012" />
    <public type="attr" name="actionModeBackground" id="0x7f030013" />
    <public type="attr" name="actionModeCloseButtonStyle" id="0x7f030014" />
    <public type="attr" name="actionModeCloseContentDescription" id="0x7f030015" />
    <public type="attr" name="actionModeCloseDrawable" id="0x7f030016" />
    <public type="attr" name="actionModeCopyDrawable" id="0x7f030017" />
    <public type="attr" name="actionModeCutDrawable" id="0x7f030018" />
    <public type="attr" name="actionModeFindDrawable" id="0x7f030019" />
    <public type="attr" name="actionModePasteDrawable" id="0x7f03001a" />
    <public type="attr" name="actionModePopupWindowStyle" id="0x7f03001b" />
    <public type="attr" name="actionModeSelectAllDrawable" id="0x7f03001c" />
    <public type="attr" name="actionModeShareDrawable" id="0x7f03001d" />
    <public type="attr" name="actionModeSplitBackground" id="0x7f03001e" />
    <public type="attr" name="actionModeStyle" id="0x7f03001f" />
    <public type="attr" name="actionModeTheme" id="0x7f030020" />
    <public type="attr" name="actionModeWebSearchDrawable" id="0x7f030021" />
    <public type="attr" name="actionOverflowButtonStyle" id="0x7f030022" />
    <public type="attr" name="actionOverflowMenuStyle" id="0x7f030023" />
    <public type="attr" name="actionProviderClass" id="0x7f030024" />
    <public type="attr" name="actionTextColorAlpha" id="0x7f030025" />
    <public type="attr" name="actionViewClass" id="0x7f030026" />
    <public type="attr" name="activeIndicatorLabelPadding" id="0x7f030027" />
    <public type="attr" name="activityChooserViewStyle" id="0x7f030028" />
    <public type="attr" name="addElevationShadow" id="0x7f030029" />
    <public type="attr" name="alertDialogButtonGroupStyle" id="0x7f03002a" />
    <public type="attr" name="alertDialogCenterButtons" id="0x7f03002b" />
    <public type="attr" name="alertDialogStyle" id="0x7f03002c" />
    <public type="attr" name="alertDialogTheme" id="0x7f03002d" />
    <public type="attr" name="allowStacking" id="0x7f03002e" />
    <public type="attr" name="alpha" id="0x7f03002f" />
    <public type="attr" name="alphabeticModifiers" id="0x7f030030" />
    <public type="attr" name="altSrc" id="0x7f030031" />
    <public type="attr" name="animateCircleAngleTo" id="0x7f030032" />
    <public type="attr" name="animateMenuItems" id="0x7f030033" />
    <public type="attr" name="animateNavigationIcon" id="0x7f030034" />
    <public type="attr" name="animateRelativeTo" id="0x7f030035" />
    <public type="attr" name="animationMode" id="0x7f030036" />
    <public type="attr" name="appBarLayoutStyle" id="0x7f030037" />
    <public type="attr" name="applyMotionScene" id="0x7f030038" />
    <public type="attr" name="arcMode" id="0x7f030039" />
    <public type="attr" name="argType" id="0x7f03003a" />
    <public type="attr" name="arrowHeadLength" id="0x7f03003b" />
    <public type="attr" name="arrowShaftLength" id="0x7f03003c" />
    <public type="attr" name="attributeName" id="0x7f03003d" />
    <public type="attr" name="autoAdjustToWithinGrandparentBounds" id="0x7f03003e" />
    <public type="attr" name="autoCompleteMode" id="0x7f03003f" />
    <public type="attr" name="autoCompleteTextViewStyle" id="0x7f030040" />
    <public type="attr" name="autoShowKeyboard" id="0x7f030041" />
    <public type="attr" name="autoSizeMaxTextSize" id="0x7f030042" />
    <public type="attr" name="autoSizeMinTextSize" id="0x7f030043" />
    <public type="attr" name="autoSizePresetSizes" id="0x7f030044" />
    <public type="attr" name="autoSizeStepGranularity" id="0x7f030045" />
    <public type="attr" name="autoSizeTextType" id="0x7f030046" />
    <public type="attr" name="autoTransition" id="0x7f030047" />
    <public type="attr" name="backHandlingEnabled" id="0x7f030048" />
    <public type="attr" name="background" id="0x7f030049" />
    <public type="attr" name="backgroundColor" id="0x7f03004a" />
    <public type="attr" name="backgroundInsetBottom" id="0x7f03004b" />
    <public type="attr" name="backgroundInsetEnd" id="0x7f03004c" />
    <public type="attr" name="backgroundInsetStart" id="0x7f03004d" />
    <public type="attr" name="backgroundInsetTop" id="0x7f03004e" />
    <public type="attr" name="backgroundOverlayColorAlpha" id="0x7f03004f" />
    <public type="attr" name="backgroundSplit" id="0x7f030050" />
    <public type="attr" name="backgroundStacked" id="0x7f030051" />
    <public type="attr" name="backgroundTint" id="0x7f030052" />
    <public type="attr" name="backgroundTintMode" id="0x7f030053" />
    <public type="attr" name="badgeGravity" id="0x7f030054" />
    <public type="attr" name="badgeHeight" id="0x7f030055" />
    <public type="attr" name="badgeRadius" id="0x7f030056" />
    <public type="attr" name="badgeShapeAppearance" id="0x7f030057" />
    <public type="attr" name="badgeShapeAppearanceOverlay" id="0x7f030058" />
    <public type="attr" name="badgeStyle" id="0x7f030059" />
    <public type="attr" name="badgeText" id="0x7f03005a" />
    <public type="attr" name="badgeTextAppearance" id="0x7f03005b" />
    <public type="attr" name="badgeTextColor" id="0x7f03005c" />
    <public type="attr" name="badgeVerticalPadding" id="0x7f03005d" />
    <public type="attr" name="badgeWidePadding" id="0x7f03005e" />
    <public type="attr" name="badgeWidth" id="0x7f03005f" />
    <public type="attr" name="badgeWithTextHeight" id="0x7f030060" />
    <public type="attr" name="badgeWithTextRadius" id="0x7f030061" />
    <public type="attr" name="badgeWithTextShapeAppearance" id="0x7f030062" />
    <public type="attr" name="badgeWithTextShapeAppearanceOverlay" id="0x7f030063" />
    <public type="attr" name="badgeWithTextWidth" id="0x7f030064" />
    <public type="attr" name="barLength" id="0x7f030065" />
    <public type="attr" name="barrierAllowsGoneWidgets" id="0x7f030066" />
    <public type="attr" name="barrierDirection" id="0x7f030067" />
    <public type="attr" name="barrierMargin" id="0x7f030068" />
    <public type="attr" name="behavior_autoHide" id="0x7f030069" />
    <public type="attr" name="behavior_autoShrink" id="0x7f03006a" />
    <public type="attr" name="behavior_draggable" id="0x7f03006b" />
    <public type="attr" name="behavior_expandedOffset" id="0x7f03006c" />
    <public type="attr" name="behavior_fitToContents" id="0x7f03006d" />
    <public type="attr" name="behavior_halfExpandedRatio" id="0x7f03006e" />
    <public type="attr" name="behavior_hideable" id="0x7f03006f" />
    <public type="attr" name="behavior_overlapTop" id="0x7f030070" />
    <public type="attr" name="behavior_peekHeight" id="0x7f030071" />
    <public type="attr" name="behavior_saveFlags" id="0x7f030072" />
    <public type="attr" name="behavior_significantVelocityThreshold" id="0x7f030073" />
    <public type="attr" name="behavior_skipCollapsed" id="0x7f030074" />
    <public type="attr" name="blendSrc" id="0x7f030075" />
    <public type="attr" name="borderRound" id="0x7f030076" />
    <public type="attr" name="borderRoundPercent" id="0x7f030077" />
    <public type="attr" name="borderWidth" id="0x7f030078" />
    <public type="attr" name="borderlessButtonStyle" id="0x7f030079" />
    <public type="attr" name="bottomAppBarStyle" id="0x7f03007a" />
    <public type="attr" name="bottomInsetScrimEnabled" id="0x7f03007b" />
    <public type="attr" name="bottomNavigationStyle" id="0x7f03007c" />
    <public type="attr" name="bottomSheetDialogTheme" id="0x7f03007d" />
    <public type="attr" name="bottomSheetDragHandleStyle" id="0x7f03007e" />
    <public type="attr" name="bottomSheetStyle" id="0x7f03007f" />
    <public type="attr" name="boxBackgroundColor" id="0x7f030080" />
    <public type="attr" name="boxBackgroundMode" id="0x7f030081" />
    <public type="attr" name="boxCollapsedPaddingTop" id="0x7f030082" />
    <public type="attr" name="boxCornerRadiusBottomEnd" id="0x7f030083" />
    <public type="attr" name="boxCornerRadiusBottomStart" id="0x7f030084" />
    <public type="attr" name="boxCornerRadiusTopEnd" id="0x7f030085" />
    <public type="attr" name="boxCornerRadiusTopStart" id="0x7f030086" />
    <public type="attr" name="boxStrokeColor" id="0x7f030087" />
    <public type="attr" name="boxStrokeErrorColor" id="0x7f030088" />
    <public type="attr" name="boxStrokeWidth" id="0x7f030089" />
    <public type="attr" name="boxStrokeWidthFocused" id="0x7f03008a" />
    <public type="attr" name="brightness" id="0x7f03008b" />
    <public type="attr" name="buttonBarButtonStyle" id="0x7f03008c" />
    <public type="attr" name="buttonBarNegativeButtonStyle" id="0x7f03008d" />
    <public type="attr" name="buttonBarNeutralButtonStyle" id="0x7f03008e" />
    <public type="attr" name="buttonBarPositiveButtonStyle" id="0x7f03008f" />
    <public type="attr" name="buttonBarStyle" id="0x7f030090" />
    <public type="attr" name="buttonCompat" id="0x7f030091" />
    <public type="attr" name="buttonGravity" id="0x7f030092" />
    <public type="attr" name="buttonIcon" id="0x7f030093" />
    <public type="attr" name="buttonIconDimen" id="0x7f030094" />
    <public type="attr" name="buttonIconTint" id="0x7f030095" />
    <public type="attr" name="buttonIconTintMode" id="0x7f030096" />
    <public type="attr" name="buttonPanelSideLayout" id="0x7f030097" />
    <public type="attr" name="buttonSize" id="0x7f030098" />
    <public type="attr" name="buttonStyle" id="0x7f030099" />
    <public type="attr" name="buttonStyleSmall" id="0x7f03009a" />
    <public type="attr" name="buttonTint" id="0x7f03009b" />
    <public type="attr" name="buttonTintMode" id="0x7f03009c" />
    <public type="attr" name="cardBackgroundColor" id="0x7f03009d" />
    <public type="attr" name="cardCornerRadius" id="0x7f03009e" />
    <public type="attr" name="cardElevation" id="0x7f03009f" />
    <public type="attr" name="cardForegroundColor" id="0x7f0300a0" />
    <public type="attr" name="cardMaxElevation" id="0x7f0300a1" />
    <public type="attr" name="cardPreventCornerOverlap" id="0x7f0300a2" />
    <public type="attr" name="cardUseCompatPadding" id="0x7f0300a3" />
    <public type="attr" name="cardViewStyle" id="0x7f0300a4" />
    <public type="attr" name="carousel_alignment" id="0x7f0300a5" />
    <public type="attr" name="carousel_backwardTransition" id="0x7f0300a6" />
    <public type="attr" name="carousel_emptyViewsBehavior" id="0x7f0300a7" />
    <public type="attr" name="carousel_firstView" id="0x7f0300a8" />
    <public type="attr" name="carousel_forwardTransition" id="0x7f0300a9" />
    <public type="attr" name="carousel_infinite" id="0x7f0300aa" />
    <public type="attr" name="carousel_nextState" id="0x7f0300ab" />
    <public type="attr" name="carousel_previousState" id="0x7f0300ac" />
    <public type="attr" name="carousel_touchUpMode" id="0x7f0300ad" />
    <public type="attr" name="carousel_touchUp_dampeningFactor" id="0x7f0300ae" />
    <public type="attr" name="carousel_touchUp_velocityThreshold" id="0x7f0300af" />
    <public type="attr" name="centerIfNoTextEnabled" id="0x7f0300b0" />
    <public type="attr" name="chainUseRtl" id="0x7f0300b1" />
    <public type="attr" name="checkMarkCompat" id="0x7f0300b2" />
    <public type="attr" name="checkMarkTint" id="0x7f0300b3" />
    <public type="attr" name="checkMarkTintMode" id="0x7f0300b4" />
    <public type="attr" name="checkboxStyle" id="0x7f0300b5" />
    <public type="attr" name="checkedButton" id="0x7f0300b6" />
    <public type="attr" name="checkedChip" id="0x7f0300b7" />
    <public type="attr" name="checkedIcon" id="0x7f0300b8" />
    <public type="attr" name="checkedIconEnabled" id="0x7f0300b9" />
    <public type="attr" name="checkedIconGravity" id="0x7f0300ba" />
    <public type="attr" name="checkedIconMargin" id="0x7f0300bb" />
    <public type="attr" name="checkedIconSize" id="0x7f0300bc" />
    <public type="attr" name="checkedIconTint" id="0x7f0300bd" />
    <public type="attr" name="checkedIconVisible" id="0x7f0300be" />
    <public type="attr" name="checkedState" id="0x7f0300bf" />
    <public type="attr" name="checkedTextViewStyle" id="0x7f0300c0" />
    <public type="attr" name="chipBackgroundColor" id="0x7f0300c1" />
    <public type="attr" name="chipCornerRadius" id="0x7f0300c2" />
    <public type="attr" name="chipEndPadding" id="0x7f0300c3" />
    <public type="attr" name="chipGroupStyle" id="0x7f0300c4" />
    <public type="attr" name="chipIcon" id="0x7f0300c5" />
    <public type="attr" name="chipIconEnabled" id="0x7f0300c6" />
    <public type="attr" name="chipIconSize" id="0x7f0300c7" />
    <public type="attr" name="chipIconTint" id="0x7f0300c8" />
    <public type="attr" name="chipIconVisible" id="0x7f0300c9" />
    <public type="attr" name="chipMinHeight" id="0x7f0300ca" />
    <public type="attr" name="chipMinTouchTargetSize" id="0x7f0300cb" />
    <public type="attr" name="chipSpacing" id="0x7f0300cc" />
    <public type="attr" name="chipSpacingHorizontal" id="0x7f0300cd" />
    <public type="attr" name="chipSpacingVertical" id="0x7f0300ce" />
    <public type="attr" name="chipStandaloneStyle" id="0x7f0300cf" />
    <public type="attr" name="chipStartPadding" id="0x7f0300d0" />
    <public type="attr" name="chipStrokeColor" id="0x7f0300d1" />
    <public type="attr" name="chipStrokeWidth" id="0x7f0300d2" />
    <public type="attr" name="chipStyle" id="0x7f0300d3" />
    <public type="attr" name="chipSurfaceColor" id="0x7f0300d4" />
    <public type="attr" name="circleCrop" id="0x7f0300d5" />
    <public type="attr" name="circleRadius" id="0x7f0300d6" />
    <public type="attr" name="circularProgressIndicatorStyle" id="0x7f0300d7" />
    <public type="attr" name="circularflow_angles" id="0x7f0300d8" />
    <public type="attr" name="circularflow_defaultAngle" id="0x7f0300d9" />
    <public type="attr" name="circularflow_defaultRadius" id="0x7f0300da" />
    <public type="attr" name="circularflow_radiusInDP" id="0x7f0300db" />
    <public type="attr" name="circularflow_viewCenter" id="0x7f0300dc" />
    <public type="attr" name="clearsTag" id="0x7f0300dd" />
    <public type="attr" name="clickAction" id="0x7f0300de" />
    <public type="attr" name="clockFaceBackgroundColor" id="0x7f0300df" />
    <public type="attr" name="clockHandColor" id="0x7f0300e0" />
    <public type="attr" name="clockIcon" id="0x7f0300e1" />
    <public type="attr" name="clockNumberTextColor" id="0x7f0300e2" />
    <public type="attr" name="closeIcon" id="0x7f0300e3" />
    <public type="attr" name="closeIconEnabled" id="0x7f0300e4" />
    <public type="attr" name="closeIconEndPadding" id="0x7f0300e5" />
    <public type="attr" name="closeIconSize" id="0x7f0300e6" />
    <public type="attr" name="closeIconStartPadding" id="0x7f0300e7" />
    <public type="attr" name="closeIconTint" id="0x7f0300e8" />
    <public type="attr" name="closeIconVisible" id="0x7f0300e9" />
    <public type="attr" name="closeItemLayout" id="0x7f0300ea" />
    <public type="attr" name="collapseContentDescription" id="0x7f0300eb" />
    <public type="attr" name="collapseIcon" id="0x7f0300ec" />
    <public type="attr" name="collapsedSize" id="0x7f0300ed" />
    <public type="attr" name="collapsedTitleGravity" id="0x7f0300ee" />
    <public type="attr" name="collapsedTitleTextAppearance" id="0x7f0300ef" />
    <public type="attr" name="collapsedTitleTextColor" id="0x7f0300f0" />
    <public type="attr" name="collapsingToolbarLayoutLargeSize" id="0x7f0300f1" />
    <public type="attr" name="collapsingToolbarLayoutLargeStyle" id="0x7f0300f2" />
    <public type="attr" name="collapsingToolbarLayoutMediumSize" id="0x7f0300f3" />
    <public type="attr" name="collapsingToolbarLayoutMediumStyle" id="0x7f0300f4" />
    <public type="attr" name="collapsingToolbarLayoutStyle" id="0x7f0300f5" />
    <public type="attr" name="color" id="0x7f0300f6" />
    <public type="attr" name="colorAccent" id="0x7f0300f7" />
    <public type="attr" name="colorBackgroundFloating" id="0x7f0300f8" />
    <public type="attr" name="colorButtonNormal" id="0x7f0300f9" />
    <public type="attr" name="colorContainer" id="0x7f0300fa" />
    <public type="attr" name="colorControlActivated" id="0x7f0300fb" />
    <public type="attr" name="colorControlHighlight" id="0x7f0300fc" />
    <public type="attr" name="colorControlNormal" id="0x7f0300fd" />
    <public type="attr" name="colorError" id="0x7f0300fe" />
    <public type="attr" name="colorErrorContainer" id="0x7f0300ff" />
    <public type="attr" name="colorOnBackground" id="0x7f030100" />
    <public type="attr" name="colorOnContainer" id="0x7f030101" />
    <public type="attr" name="colorOnContainerUnchecked" id="0x7f030102" />
    <public type="attr" name="colorOnError" id="0x7f030103" />
    <public type="attr" name="colorOnErrorContainer" id="0x7f030104" />
    <public type="attr" name="colorOnPrimary" id="0x7f030105" />
    <public type="attr" name="colorOnPrimaryContainer" id="0x7f030106" />
    <public type="attr" name="colorOnPrimaryFixed" id="0x7f030107" />
    <public type="attr" name="colorOnPrimaryFixedVariant" id="0x7f030108" />
    <public type="attr" name="colorOnPrimarySurface" id="0x7f030109" />
    <public type="attr" name="colorOnSecondary" id="0x7f03010a" />
    <public type="attr" name="colorOnSecondaryContainer" id="0x7f03010b" />
    <public type="attr" name="colorOnSecondaryFixed" id="0x7f03010c" />
    <public type="attr" name="colorOnSecondaryFixedVariant" id="0x7f03010d" />
    <public type="attr" name="colorOnSurface" id="0x7f03010e" />
    <public type="attr" name="colorOnSurfaceInverse" id="0x7f03010f" />
    <public type="attr" name="colorOnSurfaceVariant" id="0x7f030110" />
    <public type="attr" name="colorOnTertiary" id="0x7f030111" />
    <public type="attr" name="colorOnTertiaryContainer" id="0x7f030112" />
    <public type="attr" name="colorOnTertiaryFixed" id="0x7f030113" />
    <public type="attr" name="colorOnTertiaryFixedVariant" id="0x7f030114" />
    <public type="attr" name="colorOutline" id="0x7f030115" />
    <public type="attr" name="colorOutlineVariant" id="0x7f030116" />
    <public type="attr" name="colorPrimary" id="0x7f030117" />
    <public type="attr" name="colorPrimaryContainer" id="0x7f030118" />
    <public type="attr" name="colorPrimaryDark" id="0x7f030119" />
    <public type="attr" name="colorPrimaryFixed" id="0x7f03011a" />
    <public type="attr" name="colorPrimaryFixedDim" id="0x7f03011b" />
    <public type="attr" name="colorPrimaryInverse" id="0x7f03011c" />
    <public type="attr" name="colorPrimarySurface" id="0x7f03011d" />
    <public type="attr" name="colorPrimaryVariant" id="0x7f03011e" />
    <public type="attr" name="colorScheme" id="0x7f03011f" />
    <public type="attr" name="colorSecondary" id="0x7f030120" />
    <public type="attr" name="colorSecondaryContainer" id="0x7f030121" />
    <public type="attr" name="colorSecondaryFixed" id="0x7f030122" />
    <public type="attr" name="colorSecondaryFixedDim" id="0x7f030123" />
    <public type="attr" name="colorSecondaryVariant" id="0x7f030124" />
    <public type="attr" name="colorSurface" id="0x7f030125" />
    <public type="attr" name="colorSurfaceBright" id="0x7f030126" />
    <public type="attr" name="colorSurfaceContainer" id="0x7f030127" />
    <public type="attr" name="colorSurfaceContainerHigh" id="0x7f030128" />
    <public type="attr" name="colorSurfaceContainerHighest" id="0x7f030129" />
    <public type="attr" name="colorSurfaceContainerLow" id="0x7f03012a" />
    <public type="attr" name="colorSurfaceContainerLowest" id="0x7f03012b" />
    <public type="attr" name="colorSurfaceDim" id="0x7f03012c" />
    <public type="attr" name="colorSurfaceInverse" id="0x7f03012d" />
    <public type="attr" name="colorSurfaceVariant" id="0x7f03012e" />
    <public type="attr" name="colorSwitchThumbNormal" id="0x7f03012f" />
    <public type="attr" name="colorTertiary" id="0x7f030130" />
    <public type="attr" name="colorTertiaryContainer" id="0x7f030131" />
    <public type="attr" name="colorTertiaryFixed" id="0x7f030132" />
    <public type="attr" name="colorTertiaryFixedDim" id="0x7f030133" />
    <public type="attr" name="commitIcon" id="0x7f030134" />
    <public type="attr" name="compatShadowEnabled" id="0x7f030135" />
    <public type="attr" name="constraintRotate" id="0x7f030136" />
    <public type="attr" name="constraintSet" id="0x7f030137" />
    <public type="attr" name="constraintSetEnd" id="0x7f030138" />
    <public type="attr" name="constraintSetStart" id="0x7f030139" />
    <public type="attr" name="constraint_referenced_ids" id="0x7f03013a" />
    <public type="attr" name="constraint_referenced_tags" id="0x7f03013b" />
    <public type="attr" name="constraints" id="0x7f03013c" />
    <public type="attr" name="content" id="0x7f03013d" />
    <public type="attr" name="contentDescription" id="0x7f03013e" />
    <public type="attr" name="contentInsetEnd" id="0x7f03013f" />
    <public type="attr" name="contentInsetEndWithActions" id="0x7f030140" />
    <public type="attr" name="contentInsetLeft" id="0x7f030141" />
    <public type="attr" name="contentInsetRight" id="0x7f030142" />
    <public type="attr" name="contentInsetStart" id="0x7f030143" />
    <public type="attr" name="contentInsetStartWithNavigation" id="0x7f030144" />
    <public type="attr" name="contentPadding" id="0x7f030145" />
    <public type="attr" name="contentPaddingBottom" id="0x7f030146" />
    <public type="attr" name="contentPaddingEnd" id="0x7f030147" />
    <public type="attr" name="contentPaddingLeft" id="0x7f030148" />
    <public type="attr" name="contentPaddingRight" id="0x7f030149" />
    <public type="attr" name="contentPaddingStart" id="0x7f03014a" />
    <public type="attr" name="contentPaddingTop" id="0x7f03014b" />
    <public type="attr" name="contentScrim" id="0x7f03014c" />
    <public type="attr" name="contrast" id="0x7f03014d" />
    <public type="attr" name="controlBackground" id="0x7f03014e" />
    <public type="attr" name="coordinatorLayoutStyle" id="0x7f03014f" />
    <public type="attr" name="coplanarSiblingViewId" id="0x7f030150" />
    <public type="attr" name="cornerFamily" id="0x7f030151" />
    <public type="attr" name="cornerFamilyBottomLeft" id="0x7f030152" />
    <public type="attr" name="cornerFamilyBottomRight" id="0x7f030153" />
    <public type="attr" name="cornerFamilyTopLeft" id="0x7f030154" />
    <public type="attr" name="cornerFamilyTopRight" id="0x7f030155" />
    <public type="attr" name="cornerRadius" id="0x7f030156" />
    <public type="attr" name="cornerSize" id="0x7f030157" />
    <public type="attr" name="cornerSizeBottomLeft" id="0x7f030158" />
    <public type="attr" name="cornerSizeBottomRight" id="0x7f030159" />
    <public type="attr" name="cornerSizeTopLeft" id="0x7f03015a" />
    <public type="attr" name="cornerSizeTopRight" id="0x7f03015b" />
    <public type="attr" name="counterEnabled" id="0x7f03015c" />
    <public type="attr" name="counterMaxLength" id="0x7f03015d" />
    <public type="attr" name="counterOverflowTextAppearance" id="0x7f03015e" />
    <public type="attr" name="counterOverflowTextColor" id="0x7f03015f" />
    <public type="attr" name="counterTextAppearance" id="0x7f030160" />
    <public type="attr" name="counterTextColor" id="0x7f030161" />
    <public type="attr" name="crossfade" id="0x7f030162" />
    <public type="attr" name="currentState" id="0x7f030163" />
    <public type="attr" name="cursorColor" id="0x7f030164" />
    <public type="attr" name="cursorErrorColor" id="0x7f030165" />
    <public type="attr" name="curveFit" id="0x7f030166" />
    <public type="attr" name="customBoolean" id="0x7f030167" />
    <public type="attr" name="customColorDrawableValue" id="0x7f030168" />
    <public type="attr" name="customColorValue" id="0x7f030169" />
    <public type="attr" name="customDimension" id="0x7f03016a" />
    <public type="attr" name="customFloatValue" id="0x7f03016b" />
    <public type="attr" name="customIntegerValue" id="0x7f03016c" />
    <public type="attr" name="customNavigationLayout" id="0x7f03016d" />
    <public type="attr" name="customPixelDimension" id="0x7f03016e" />
    <public type="attr" name="customReference" id="0x7f03016f" />
    <public type="attr" name="customStringValue" id="0x7f030170" />
    <public type="attr" name="data" id="0x7f030171" />
    <public type="attr" name="dataPattern" id="0x7f030172" />
    <public type="attr" name="dayInvalidStyle" id="0x7f030173" />
    <public type="attr" name="daySelectedStyle" id="0x7f030174" />
    <public type="attr" name="dayStyle" id="0x7f030175" />
    <public type="attr" name="dayTodayStyle" id="0x7f030176" />
    <public type="attr" name="defaultDuration" id="0x7f030177" />
    <public type="attr" name="defaultMarginsEnabled" id="0x7f030178" />
    <public type="attr" name="defaultQueryHint" id="0x7f030179" />
    <public type="attr" name="defaultScrollFlagsEnabled" id="0x7f03017a" />
    <public type="attr" name="defaultState" id="0x7f03017b" />
    <public type="attr" name="deltaPolarAngle" id="0x7f03017c" />
    <public type="attr" name="deltaPolarRadius" id="0x7f03017d" />
    <public type="attr" name="deriveConstraintsFrom" id="0x7f03017e" />
    <public type="attr" name="destination" id="0x7f03017f" />
    <public type="attr" name="dialogCornerRadius" id="0x7f030180" />
    <public type="attr" name="dialogPreferredPadding" id="0x7f030181" />
    <public type="attr" name="dialogTheme" id="0x7f030182" />
    <public type="attr" name="displayOptions" id="0x7f030183" />
    <public type="attr" name="divider" id="0x7f030184" />
    <public type="attr" name="dividerColor" id="0x7f030185" />
    <public type="attr" name="dividerHorizontal" id="0x7f030186" />
    <public type="attr" name="dividerInsetEnd" id="0x7f030187" />
    <public type="attr" name="dividerInsetStart" id="0x7f030188" />
    <public type="attr" name="dividerPadding" id="0x7f030189" />
    <public type="attr" name="dividerThickness" id="0x7f03018a" />
    <public type="attr" name="dividerVertical" id="0x7f03018b" />
    <public type="attr" name="dragDirection" id="0x7f03018c" />
    <public type="attr" name="dragScale" id="0x7f03018d" />
    <public type="attr" name="dragThreshold" id="0x7f03018e" />
    <public type="attr" name="drawPath" id="0x7f03018f" />
    <public type="attr" name="drawableBottomCompat" id="0x7f030190" />
    <public type="attr" name="drawableEndCompat" id="0x7f030191" />
    <public type="attr" name="drawableLeftCompat" id="0x7f030192" />
    <public type="attr" name="drawableRightCompat" id="0x7f030193" />
    <public type="attr" name="drawableSize" id="0x7f030194" />
    <public type="attr" name="drawableStartCompat" id="0x7f030195" />
    <public type="attr" name="drawableTint" id="0x7f030196" />
    <public type="attr" name="drawableTintMode" id="0x7f030197" />
    <public type="attr" name="drawableTopCompat" id="0x7f030198" />
    <public type="attr" name="drawerArrowStyle" id="0x7f030199" />
    <public type="attr" name="drawerLayoutCornerSize" id="0x7f03019a" />
    <public type="attr" name="drawerLayoutStyle" id="0x7f03019b" />
    <public type="attr" name="dropDownBackgroundTint" id="0x7f03019c" />
    <public type="attr" name="dropDownListViewStyle" id="0x7f03019d" />
    <public type="attr" name="dropdownListPreferredItemHeight" id="0x7f03019e" />
    <public type="attr" name="duration" id="0x7f03019f" />
    <public type="attr" name="dynamicColorThemeOverlay" id="0x7f0301a0" />
    <public type="attr" name="editTextBackground" id="0x7f0301a1" />
    <public type="attr" name="editTextColor" id="0x7f0301a2" />
    <public type="attr" name="editTextStyle" id="0x7f0301a3" />
    <public type="attr" name="elevation" id="0x7f0301a4" />
    <public type="attr" name="elevationOverlayAccentColor" id="0x7f0301a5" />
    <public type="attr" name="elevationOverlayColor" id="0x7f0301a6" />
    <public type="attr" name="elevationOverlayEnabled" id="0x7f0301a7" />
    <public type="attr" name="emojiCompatEnabled" id="0x7f0301a8" />
    <public type="attr" name="enableEdgeToEdge" id="0x7f0301a9" />
    <public type="attr" name="endIconCheckable" id="0x7f0301aa" />
    <public type="attr" name="endIconContentDescription" id="0x7f0301ab" />
    <public type="attr" name="endIconDrawable" id="0x7f0301ac" />
    <public type="attr" name="endIconMinSize" id="0x7f0301ad" />
    <public type="attr" name="endIconMode" id="0x7f0301ae" />
    <public type="attr" name="endIconScaleType" id="0x7f0301af" />
    <public type="attr" name="endIconTint" id="0x7f0301b0" />
    <public type="attr" name="endIconTintMode" id="0x7f0301b1" />
    <public type="attr" name="enforceMaterialTheme" id="0x7f0301b2" />
    <public type="attr" name="enforceTextAppearance" id="0x7f0301b3" />
    <public type="attr" name="ensureMinTouchTargetSize" id="0x7f0301b4" />
    <public type="attr" name="enterAnim" id="0x7f0301b5" />
    <public type="attr" name="errorAccessibilityLabel" id="0x7f0301b6" />
    <public type="attr" name="errorAccessibilityLiveRegion" id="0x7f0301b7" />
    <public type="attr" name="errorContentDescription" id="0x7f0301b8" />
    <public type="attr" name="errorEnabled" id="0x7f0301b9" />
    <public type="attr" name="errorIconDrawable" id="0x7f0301ba" />
    <public type="attr" name="errorIconTint" id="0x7f0301bb" />
    <public type="attr" name="errorIconTintMode" id="0x7f0301bc" />
    <public type="attr" name="errorShown" id="0x7f0301bd" />
    <public type="attr" name="errorTextAppearance" id="0x7f0301be" />
    <public type="attr" name="errorTextColor" id="0x7f0301bf" />
    <public type="attr" name="exitAnim" id="0x7f0301c0" />
    <public type="attr" name="expandActivityOverflowButtonDrawable" id="0x7f0301c1" />
    <public type="attr" name="expanded" id="0x7f0301c2" />
    <public type="attr" name="expandedHintEnabled" id="0x7f0301c3" />
    <public type="attr" name="expandedTitleGravity" id="0x7f0301c4" />
    <public type="attr" name="expandedTitleMargin" id="0x7f0301c5" />
    <public type="attr" name="expandedTitleMarginBottom" id="0x7f0301c6" />
    <public type="attr" name="expandedTitleMarginEnd" id="0x7f0301c7" />
    <public type="attr" name="expandedTitleMarginStart" id="0x7f0301c8" />
    <public type="attr" name="expandedTitleMarginTop" id="0x7f0301c9" />
    <public type="attr" name="expandedTitleTextAppearance" id="0x7f0301ca" />
    <public type="attr" name="expandedTitleTextColor" id="0x7f0301cb" />
    <public type="attr" name="extendMotionSpec" id="0x7f0301cc" />
    <public type="attr" name="extendStrategy" id="0x7f0301cd" />
    <public type="attr" name="extendedFloatingActionButtonPrimaryStyle" id="0x7f0301ce" />
    <public type="attr" name="extendedFloatingActionButtonSecondaryStyle" id="0x7f0301cf" />
    <public type="attr" name="extendedFloatingActionButtonStyle" id="0x7f0301d0" />
    <public type="attr" name="extendedFloatingActionButtonSurfaceStyle" id="0x7f0301d1" />
    <public type="attr" name="extendedFloatingActionButtonTertiaryStyle" id="0x7f0301d2" />
    <public type="attr" name="extraMultilineHeightEnabled" id="0x7f0301d3" />
    <public type="attr" name="fabAlignmentMode" id="0x7f0301d4" />
    <public type="attr" name="fabAlignmentModeEndMargin" id="0x7f0301d5" />
    <public type="attr" name="fabAnchorMode" id="0x7f0301d6" />
    <public type="attr" name="fabAnimationMode" id="0x7f0301d7" />
    <public type="attr" name="fabCradleMargin" id="0x7f0301d8" />
    <public type="attr" name="fabCradleRoundedCornerRadius" id="0x7f0301d9" />
    <public type="attr" name="fabCradleVerticalOffset" id="0x7f0301da" />
    <public type="attr" name="fabCustomSize" id="0x7f0301db" />
    <public type="attr" name="fabSize" id="0x7f0301dc" />
    <public type="attr" name="fastScrollEnabled" id="0x7f0301dd" />
    <public type="attr" name="fastScrollHorizontalThumbDrawable" id="0x7f0301de" />
    <public type="attr" name="fastScrollHorizontalTrackDrawable" id="0x7f0301df" />
    <public type="attr" name="fastScrollVerticalThumbDrawable" id="0x7f0301e0" />
    <public type="attr" name="fastScrollVerticalTrackDrawable" id="0x7f0301e1" />
    <public type="attr" name="firstBaselineToTopHeight" id="0x7f0301e2" />
    <public type="attr" name="floatingActionButtonLargePrimaryStyle" id="0x7f0301e3" />
    <public type="attr" name="floatingActionButtonLargeSecondaryStyle" id="0x7f0301e4" />
    <public type="attr" name="floatingActionButtonLargeStyle" id="0x7f0301e5" />
    <public type="attr" name="floatingActionButtonLargeSurfaceStyle" id="0x7f0301e6" />
    <public type="attr" name="floatingActionButtonLargeTertiaryStyle" id="0x7f0301e7" />
    <public type="attr" name="floatingActionButtonPrimaryStyle" id="0x7f0301e8" />
    <public type="attr" name="floatingActionButtonSecondaryStyle" id="0x7f0301e9" />
    <public type="attr" name="floatingActionButtonSmallPrimaryStyle" id="0x7f0301ea" />
    <public type="attr" name="floatingActionButtonSmallSecondaryStyle" id="0x7f0301eb" />
    <public type="attr" name="floatingActionButtonSmallStyle" id="0x7f0301ec" />
    <public type="attr" name="floatingActionButtonSmallSurfaceStyle" id="0x7f0301ed" />
    <public type="attr" name="floatingActionButtonSmallTertiaryStyle" id="0x7f0301ee" />
    <public type="attr" name="floatingActionButtonStyle" id="0x7f0301ef" />
    <public type="attr" name="floatingActionButtonSurfaceStyle" id="0x7f0301f0" />
    <public type="attr" name="floatingActionButtonTertiaryStyle" id="0x7f0301f1" />
    <public type="attr" name="flow_firstHorizontalBias" id="0x7f0301f2" />
    <public type="attr" name="flow_firstHorizontalStyle" id="0x7f0301f3" />
    <public type="attr" name="flow_firstVerticalBias" id="0x7f0301f4" />
    <public type="attr" name="flow_firstVerticalStyle" id="0x7f0301f5" />
    <public type="attr" name="flow_horizontalAlign" id="0x7f0301f6" />
    <public type="attr" name="flow_horizontalBias" id="0x7f0301f7" />
    <public type="attr" name="flow_horizontalGap" id="0x7f0301f8" />
    <public type="attr" name="flow_horizontalStyle" id="0x7f0301f9" />
    <public type="attr" name="flow_lastHorizontalBias" id="0x7f0301fa" />
    <public type="attr" name="flow_lastHorizontalStyle" id="0x7f0301fb" />
    <public type="attr" name="flow_lastVerticalBias" id="0x7f0301fc" />
    <public type="attr" name="flow_lastVerticalStyle" id="0x7f0301fd" />
    <public type="attr" name="flow_maxElementsWrap" id="0x7f0301fe" />
    <public type="attr" name="flow_padding" id="0x7f0301ff" />
    <public type="attr" name="flow_verticalAlign" id="0x7f030200" />
    <public type="attr" name="flow_verticalBias" id="0x7f030201" />
    <public type="attr" name="flow_verticalGap" id="0x7f030202" />
    <public type="attr" name="flow_verticalStyle" id="0x7f030203" />
    <public type="attr" name="flow_wrapMode" id="0x7f030204" />
    <public type="attr" name="font" id="0x7f030205" />
    <public type="attr" name="fontFamily" id="0x7f030206" />
    <public type="attr" name="fontProviderAuthority" id="0x7f030207" />
    <public type="attr" name="fontProviderCerts" id="0x7f030208" />
    <public type="attr" name="fontProviderFetchStrategy" id="0x7f030209" />
    <public type="attr" name="fontProviderFetchTimeout" id="0x7f03020a" />
    <public type="attr" name="fontProviderPackage" id="0x7f03020b" />
    <public type="attr" name="fontProviderQuery" id="0x7f03020c" />
    <public type="attr" name="fontProviderSystemFontFamily" id="0x7f03020d" />
    <public type="attr" name="fontStyle" id="0x7f03020e" />
    <public type="attr" name="fontVariationSettings" id="0x7f03020f" />
    <public type="attr" name="fontWeight" id="0x7f030210" />
    <public type="attr" name="forceApplySystemWindowInsetTop" id="0x7f030211" />
    <public type="attr" name="forceDefaultNavigationOnClickListener" id="0x7f030212" />
    <public type="attr" name="foregroundInsidePadding" id="0x7f030213" />
    <public type="attr" name="framePosition" id="0x7f030214" />
    <public type="attr" name="gapBetweenBars" id="0x7f030215" />
    <public type="attr" name="gestureInsetBottomIgnored" id="0x7f030216" />
    <public type="attr" name="goIcon" id="0x7f030217" />
    <public type="attr" name="graph" id="0x7f030218" />
    <public type="attr" name="guidelineUseRtl" id="0x7f030219" />
    <public type="attr" name="haloColor" id="0x7f03021a" />
    <public type="attr" name="haloRadius" id="0x7f03021b" />
    <public type="attr" name="headerLayout" id="0x7f03021c" />
    <public type="attr" name="height" id="0x7f03021d" />
    <public type="attr" name="helperText" id="0x7f03021e" />
    <public type="attr" name="helperTextEnabled" id="0x7f03021f" />
    <public type="attr" name="helperTextTextAppearance" id="0x7f030220" />
    <public type="attr" name="helperTextTextColor" id="0x7f030221" />
    <public type="attr" name="hideAnimationBehavior" id="0x7f030222" />
    <public type="attr" name="hideMotionSpec" id="0x7f030223" />
    <public type="attr" name="hideNavigationIcon" id="0x7f030224" />
    <public type="attr" name="hideOnContentScroll" id="0x7f030225" />
    <public type="attr" name="hideOnScroll" id="0x7f030226" />
    <public type="attr" name="hintAnimationEnabled" id="0x7f030227" />
    <public type="attr" name="hintEnabled" id="0x7f030228" />
    <public type="attr" name="hintTextAppearance" id="0x7f030229" />
    <public type="attr" name="hintTextColor" id="0x7f03022a" />
    <public type="attr" name="homeAsUpIndicator" id="0x7f03022b" />
    <public type="attr" name="homeLayout" id="0x7f03022c" />
    <public type="attr" name="horizontalOffset" id="0x7f03022d" />
    <public type="attr" name="horizontalOffsetWithText" id="0x7f03022e" />
    <public type="attr" name="hoveredFocusedTranslationZ" id="0x7f03022f" />
    <public type="attr" name="ico_animations" id="0x7f030230" />
    <public type="attr" name="ico_automirror" id="0x7f030231" />
    <public type="attr" name="ico_background_color" id="0x7f030232" />
    <public type="attr" name="ico_background_contour_color" id="0x7f030233" />
    <public type="attr" name="ico_background_contour_width" id="0x7f030234" />
    <public type="attr" name="ico_color" id="0x7f030235" />
    <public type="attr" name="ico_contour_color" id="0x7f030236" />
    <public type="attr" name="ico_contour_width" id="0x7f030237" />
    <public type="attr" name="ico_corner_radius" id="0x7f030238" />
    <public type="attr" name="ico_icon" id="0x7f030239" />
    <public type="attr" name="ico_offset_x" id="0x7f03023a" />
    <public type="attr" name="ico_offset_y" id="0x7f03023b" />
    <public type="attr" name="ico_padding" id="0x7f03023c" />
    <public type="attr" name="ico_shadow_color" id="0x7f03023d" />
    <public type="attr" name="ico_shadow_dx" id="0x7f03023e" />
    <public type="attr" name="ico_shadow_dy" id="0x7f03023f" />
    <public type="attr" name="ico_shadow_radius" id="0x7f030240" />
    <public type="attr" name="ico_size" id="0x7f030241" />
    <public type="attr" name="icon" id="0x7f030242" />
    <public type="attr" name="iconEndPadding" id="0x7f030243" />
    <public type="attr" name="iconGravity" id="0x7f030244" />
    <public type="attr" name="iconPadding" id="0x7f030245" />
    <public type="attr" name="iconSize" id="0x7f030246" />
    <public type="attr" name="iconStartPadding" id="0x7f030247" />
    <public type="attr" name="iconTint" id="0x7f030248" />
    <public type="attr" name="iconTintMode" id="0x7f030249" />
    <public type="attr" name="iconifiedByDefault" id="0x7f03024a" />
    <public type="attr" name="ifTagNotSet" id="0x7f03024b" />
    <public type="attr" name="ifTagSet" id="0x7f03024c" />
    <public type="attr" name="imageAspectRatio" id="0x7f03024d" />
    <public type="attr" name="imageAspectRatioAdjust" id="0x7f03024e" />
    <public type="attr" name="imageButtonStyle" id="0x7f03024f" />
    <public type="attr" name="imagePanX" id="0x7f030250" />
    <public type="attr" name="imagePanY" id="0x7f030251" />
    <public type="attr" name="imageRotate" id="0x7f030252" />
    <public type="attr" name="imageZoom" id="0x7f030253" />
    <public type="attr" name="indeterminateAnimationType" id="0x7f030254" />
    <public type="attr" name="indeterminateProgressStyle" id="0x7f030255" />
    <public type="attr" name="indicatorColor" id="0x7f030256" />
    <public type="attr" name="indicatorDirectionCircular" id="0x7f030257" />
    <public type="attr" name="indicatorDirectionLinear" id="0x7f030258" />
    <public type="attr" name="indicatorInset" id="0x7f030259" />
    <public type="attr" name="indicatorSize" id="0x7f03025a" />
    <public type="attr" name="indicatorTrackGapSize" id="0x7f03025b" />
    <public type="attr" name="initialActivityCount" id="0x7f03025c" />
    <public type="attr" name="insetForeground" id="0x7f03025d" />
    <public type="attr" name="isLightTheme" id="0x7f03025e" />
    <public type="attr" name="isMaterial3DynamicColorApplied" id="0x7f03025f" />
    <public type="attr" name="isMaterial3Theme" id="0x7f030260" />
    <public type="attr" name="isMaterialTheme" id="0x7f030261" />
    <public type="attr" name="itemActiveIndicatorStyle" id="0x7f030262" />
    <public type="attr" name="itemBackground" id="0x7f030263" />
    <public type="attr" name="itemFillColor" id="0x7f030264" />
    <public type="attr" name="itemHorizontalPadding" id="0x7f030265" />
    <public type="attr" name="itemHorizontalTranslationEnabled" id="0x7f030266" />
    <public type="attr" name="itemIconPadding" id="0x7f030267" />
    <public type="attr" name="itemIconSize" id="0x7f030268" />
    <public type="attr" name="itemIconTint" id="0x7f030269" />
    <public type="attr" name="itemMaxLines" id="0x7f03026a" />
    <public type="attr" name="itemMinHeight" id="0x7f03026b" />
    <public type="attr" name="itemPadding" id="0x7f03026c" />
    <public type="attr" name="itemPaddingBottom" id="0x7f03026d" />
    <public type="attr" name="itemPaddingTop" id="0x7f03026e" />
    <public type="attr" name="itemRippleColor" id="0x7f03026f" />
    <public type="attr" name="itemShapeAppearance" id="0x7f030270" />
    <public type="attr" name="itemShapeAppearanceOverlay" id="0x7f030271" />
    <public type="attr" name="itemShapeFillColor" id="0x7f030272" />
    <public type="attr" name="itemShapeInsetBottom" id="0x7f030273" />
    <public type="attr" name="itemShapeInsetEnd" id="0x7f030274" />
    <public type="attr" name="itemShapeInsetStart" id="0x7f030275" />
    <public type="attr" name="itemShapeInsetTop" id="0x7f030276" />
    <public type="attr" name="itemSpacing" id="0x7f030277" />
    <public type="attr" name="itemStrokeColor" id="0x7f030278" />
    <public type="attr" name="itemStrokeWidth" id="0x7f030279" />
    <public type="attr" name="itemTextAppearance" id="0x7f03027a" />
    <public type="attr" name="itemTextAppearanceActive" id="0x7f03027b" />
    <public type="attr" name="itemTextAppearanceActiveBoldEnabled" id="0x7f03027c" />
    <public type="attr" name="itemTextAppearanceInactive" id="0x7f03027d" />
    <public type="attr" name="itemTextColor" id="0x7f03027e" />
    <public type="attr" name="itemVerticalPadding" id="0x7f03027f" />
    <public type="attr" name="keyPositionType" id="0x7f030280" />
    <public type="attr" name="keyboardIcon" id="0x7f030281" />
    <public type="attr" name="keylines" id="0x7f030282" />
    <public type="attr" name="lStar" id="0x7f030283" />
    <public type="attr" name="labelBehavior" id="0x7f030284" />
    <public type="attr" name="labelStyle" id="0x7f030285" />
    <public type="attr" name="labelVisibilityMode" id="0x7f030286" />
    <public type="attr" name="largeFontVerticalOffsetAdjustment" id="0x7f030287" />
    <public type="attr" name="lastBaselineToBottomHeight" id="0x7f030288" />
    <public type="attr" name="lastItemDecorated" id="0x7f030289" />
    <public type="attr" name="launchSingleTop" id="0x7f03028a" />
    <public type="attr" name="layout" id="0x7f03028b" />
    <public type="attr" name="layoutDescription" id="0x7f03028c" />
    <public type="attr" name="layoutDuringTransition" id="0x7f03028d" />
    <public type="attr" name="layoutManager" id="0x7f03028e" />
    <public type="attr" name="layout_anchor" id="0x7f03028f" />
    <public type="attr" name="layout_anchorGravity" id="0x7f030290" />
    <public type="attr" name="layout_behavior" id="0x7f030291" />
    <public type="attr" name="layout_collapseMode" id="0x7f030292" />
    <public type="attr" name="layout_collapseParallaxMultiplier" id="0x7f030293" />
    <public type="attr" name="layout_constrainedHeight" id="0x7f030294" />
    <public type="attr" name="layout_constrainedWidth" id="0x7f030295" />
    <public type="attr" name="layout_constraintBaseline_creator" id="0x7f030296" />
    <public type="attr" name="layout_constraintBaseline_toBaselineOf" id="0x7f030297" />
    <public type="attr" name="layout_constraintBaseline_toBottomOf" id="0x7f030298" />
    <public type="attr" name="layout_constraintBaseline_toTopOf" id="0x7f030299" />
    <public type="attr" name="layout_constraintBottom_creator" id="0x7f03029a" />
    <public type="attr" name="layout_constraintBottom_toBottomOf" id="0x7f03029b" />
    <public type="attr" name="layout_constraintBottom_toTopOf" id="0x7f03029c" />
    <public type="attr" name="layout_constraintCircle" id="0x7f03029d" />
    <public type="attr" name="layout_constraintCircleAngle" id="0x7f03029e" />
    <public type="attr" name="layout_constraintCircleRadius" id="0x7f03029f" />
    <public type="attr" name="layout_constraintDimensionRatio" id="0x7f0302a0" />
    <public type="attr" name="layout_constraintEnd_toEndOf" id="0x7f0302a1" />
    <public type="attr" name="layout_constraintEnd_toStartOf" id="0x7f0302a2" />
    <public type="attr" name="layout_constraintGuide_begin" id="0x7f0302a3" />
    <public type="attr" name="layout_constraintGuide_end" id="0x7f0302a4" />
    <public type="attr" name="layout_constraintGuide_percent" id="0x7f0302a5" />
    <public type="attr" name="layout_constraintHeight" id="0x7f0302a6" />
    <public type="attr" name="layout_constraintHeight_default" id="0x7f0302a7" />
    <public type="attr" name="layout_constraintHeight_max" id="0x7f0302a8" />
    <public type="attr" name="layout_constraintHeight_min" id="0x7f0302a9" />
    <public type="attr" name="layout_constraintHeight_percent" id="0x7f0302aa" />
    <public type="attr" name="layout_constraintHorizontal_bias" id="0x7f0302ab" />
    <public type="attr" name="layout_constraintHorizontal_chainStyle" id="0x7f0302ac" />
    <public type="attr" name="layout_constraintHorizontal_weight" id="0x7f0302ad" />
    <public type="attr" name="layout_constraintLeft_creator" id="0x7f0302ae" />
    <public type="attr" name="layout_constraintLeft_toLeftOf" id="0x7f0302af" />
    <public type="attr" name="layout_constraintLeft_toRightOf" id="0x7f0302b0" />
    <public type="attr" name="layout_constraintRight_creator" id="0x7f0302b1" />
    <public type="attr" name="layout_constraintRight_toLeftOf" id="0x7f0302b2" />
    <public type="attr" name="layout_constraintRight_toRightOf" id="0x7f0302b3" />
    <public type="attr" name="layout_constraintStart_toEndOf" id="0x7f0302b4" />
    <public type="attr" name="layout_constraintStart_toStartOf" id="0x7f0302b5" />
    <public type="attr" name="layout_constraintTag" id="0x7f0302b6" />
    <public type="attr" name="layout_constraintTop_creator" id="0x7f0302b7" />
    <public type="attr" name="layout_constraintTop_toBottomOf" id="0x7f0302b8" />
    <public type="attr" name="layout_constraintTop_toTopOf" id="0x7f0302b9" />
    <public type="attr" name="layout_constraintVertical_bias" id="0x7f0302ba" />
    <public type="attr" name="layout_constraintVertical_chainStyle" id="0x7f0302bb" />
    <public type="attr" name="layout_constraintVertical_weight" id="0x7f0302bc" />
    <public type="attr" name="layout_constraintWidth" id="0x7f0302bd" />
    <public type="attr" name="layout_constraintWidth_default" id="0x7f0302be" />
    <public type="attr" name="layout_constraintWidth_max" id="0x7f0302bf" />
    <public type="attr" name="layout_constraintWidth_min" id="0x7f0302c0" />
    <public type="attr" name="layout_constraintWidth_percent" id="0x7f0302c1" />
    <public type="attr" name="layout_dodgeInsetEdges" id="0x7f0302c2" />
    <public type="attr" name="layout_editor_absoluteX" id="0x7f0302c3" />
    <public type="attr" name="layout_editor_absoluteY" id="0x7f0302c4" />
    <public type="attr" name="layout_goneMarginBaseline" id="0x7f0302c5" />
    <public type="attr" name="layout_goneMarginBottom" id="0x7f0302c6" />
    <public type="attr" name="layout_goneMarginEnd" id="0x7f0302c7" />
    <public type="attr" name="layout_goneMarginLeft" id="0x7f0302c8" />
    <public type="attr" name="layout_goneMarginRight" id="0x7f0302c9" />
    <public type="attr" name="layout_goneMarginStart" id="0x7f0302ca" />
    <public type="attr" name="layout_goneMarginTop" id="0x7f0302cb" />
    <public type="attr" name="layout_insetEdge" id="0x7f0302cc" />
    <public type="attr" name="layout_keyline" id="0x7f0302cd" />
    <public type="attr" name="layout_marginBaseline" id="0x7f0302ce" />
    <public type="attr" name="layout_optimizationLevel" id="0x7f0302cf" />
    <public type="attr" name="layout_scrollEffect" id="0x7f0302d0" />
    <public type="attr" name="layout_scrollFlags" id="0x7f0302d1" />
    <public type="attr" name="layout_scrollInterpolator" id="0x7f0302d2" />
    <public type="attr" name="layout_wrapBehaviorInParent" id="0x7f0302d3" />
    <public type="attr" name="liftOnScroll" id="0x7f0302d4" />
    <public type="attr" name="liftOnScrollColor" id="0x7f0302d5" />
    <public type="attr" name="liftOnScrollTargetViewId" id="0x7f0302d6" />
    <public type="attr" name="limitBoundsTo" id="0x7f0302d7" />
    <public type="attr" name="lineHeight" id="0x7f0302d8" />
    <public type="attr" name="lineSpacing" id="0x7f0302d9" />
    <public type="attr" name="linearProgressIndicatorStyle" id="0x7f0302da" />
    <public type="attr" name="listChoiceBackgroundIndicator" id="0x7f0302db" />
    <public type="attr" name="listChoiceIndicatorMultipleAnimated" id="0x7f0302dc" />
    <public type="attr" name="listChoiceIndicatorSingleAnimated" id="0x7f0302dd" />
    <public type="attr" name="listDividerAlertDialog" id="0x7f0302de" />
    <public type="attr" name="listItemLayout" id="0x7f0302df" />
    <public type="attr" name="listLayout" id="0x7f0302e0" />
    <public type="attr" name="listMenuViewStyle" id="0x7f0302e1" />
    <public type="attr" name="listPopupWindowStyle" id="0x7f0302e2" />
    <public type="attr" name="listPreferredItemHeight" id="0x7f0302e3" />
    <public type="attr" name="listPreferredItemHeightLarge" id="0x7f0302e4" />
    <public type="attr" name="listPreferredItemHeightSmall" id="0x7f0302e5" />
    <public type="attr" name="listPreferredItemPaddingEnd" id="0x7f0302e6" />
    <public type="attr" name="listPreferredItemPaddingLeft" id="0x7f0302e7" />
    <public type="attr" name="listPreferredItemPaddingRight" id="0x7f0302e8" />
    <public type="attr" name="listPreferredItemPaddingStart" id="0x7f0302e9" />
    <public type="attr" name="logo" id="0x7f0302ea" />
    <public type="attr" name="logoAdjustViewBounds" id="0x7f0302eb" />
    <public type="attr" name="logoDescription" id="0x7f0302ec" />
    <public type="attr" name="logoScaleType" id="0x7f0302ed" />
    <public type="attr" name="lottieAnimationViewStyle" id="0x7f0302ee" />
    <public type="attr" name="lottie_asyncUpdates" id="0x7f0302ef" />
    <public type="attr" name="lottie_autoPlay" id="0x7f0302f0" />
    <public type="attr" name="lottie_cacheComposition" id="0x7f0302f1" />
    <public type="attr" name="lottie_clipTextToBoundingBox" id="0x7f0302f2" />
    <public type="attr" name="lottie_clipToCompositionBounds" id="0x7f0302f3" />
    <public type="attr" name="lottie_colorFilter" id="0x7f0302f4" />
    <public type="attr" name="lottie_defaultFontFileExtension" id="0x7f0302f5" />
    <public type="attr" name="lottie_enableMergePathsForKitKatAndAbove" id="0x7f0302f6" />
    <public type="attr" name="lottie_fallbackRes" id="0x7f0302f7" />
    <public type="attr" name="lottie_fileName" id="0x7f0302f8" />
    <public type="attr" name="lottie_ignoreDisabledSystemAnimations" id="0x7f0302f9" />
    <public type="attr" name="lottie_imageAssetsFolder" id="0x7f0302fa" />
    <public type="attr" name="lottie_loop" id="0x7f0302fb" />
    <public type="attr" name="lottie_progress" id="0x7f0302fc" />
    <public type="attr" name="lottie_rawRes" id="0x7f0302fd" />
    <public type="attr" name="lottie_renderMode" id="0x7f0302fe" />
    <public type="attr" name="lottie_repeatCount" id="0x7f0302ff" />
    <public type="attr" name="lottie_repeatMode" id="0x7f030300" />
    <public type="attr" name="lottie_speed" id="0x7f030301" />
    <public type="attr" name="lottie_url" id="0x7f030302" />
    <public type="attr" name="lottie_useCompositionFrameRate" id="0x7f030303" />
    <public type="attr" name="marginHorizontal" id="0x7f030304" />
    <public type="attr" name="marginLeftSystemWindowInsets" id="0x7f030305" />
    <public type="attr" name="marginRightSystemWindowInsets" id="0x7f030306" />
    <public type="attr" name="marginTopSystemWindowInsets" id="0x7f030307" />
    <public type="attr" name="materialAlertDialogBodyTextStyle" id="0x7f030308" />
    <public type="attr" name="materialAlertDialogButtonSpacerVisibility" id="0x7f030309" />
    <public type="attr" name="materialAlertDialogTheme" id="0x7f03030a" />
    <public type="attr" name="materialAlertDialogTitleIconStyle" id="0x7f03030b" />
    <public type="attr" name="materialAlertDialogTitlePanelStyle" id="0x7f03030c" />
    <public type="attr" name="materialAlertDialogTitleTextStyle" id="0x7f03030d" />
    <public type="attr" name="materialButtonOutlinedStyle" id="0x7f03030e" />
    <public type="attr" name="materialButtonStyle" id="0x7f03030f" />
    <public type="attr" name="materialButtonToggleGroupStyle" id="0x7f030310" />
    <public type="attr" name="materialCalendarDay" id="0x7f030311" />
    <public type="attr" name="materialCalendarDayOfWeekLabel" id="0x7f030312" />
    <public type="attr" name="materialCalendarFullscreenTheme" id="0x7f030313" />
    <public type="attr" name="materialCalendarHeaderCancelButton" id="0x7f030314" />
    <public type="attr" name="materialCalendarHeaderConfirmButton" id="0x7f030315" />
    <public type="attr" name="materialCalendarHeaderDivider" id="0x7f030316" />
    <public type="attr" name="materialCalendarHeaderLayout" id="0x7f030317" />
    <public type="attr" name="materialCalendarHeaderSelection" id="0x7f030318" />
    <public type="attr" name="materialCalendarHeaderTitle" id="0x7f030319" />
    <public type="attr" name="materialCalendarHeaderToggleButton" id="0x7f03031a" />
    <public type="attr" name="materialCalendarMonth" id="0x7f03031b" />
    <public type="attr" name="materialCalendarMonthNavigationButton" id="0x7f03031c" />
    <public type="attr" name="materialCalendarStyle" id="0x7f03031d" />
    <public type="attr" name="materialCalendarTheme" id="0x7f03031e" />
    <public type="attr" name="materialCalendarYearNavigationButton" id="0x7f03031f" />
    <public type="attr" name="materialCardViewElevatedStyle" id="0x7f030320" />
    <public type="attr" name="materialCardViewFilledStyle" id="0x7f030321" />
    <public type="attr" name="materialCardViewOutlinedStyle" id="0x7f030322" />
    <public type="attr" name="materialCardViewStyle" id="0x7f030323" />
    <public type="attr" name="materialCircleRadius" id="0x7f030324" />
    <public type="attr" name="materialClockStyle" id="0x7f030325" />
    <public type="attr" name="materialDisplayDividerStyle" id="0x7f030326" />
    <public type="attr" name="materialDividerHeavyStyle" id="0x7f030327" />
    <public type="attr" name="materialDividerStyle" id="0x7f030328" />
    <public type="attr" name="materialIconButtonFilledStyle" id="0x7f030329" />
    <public type="attr" name="materialIconButtonFilledTonalStyle" id="0x7f03032a" />
    <public type="attr" name="materialIconButtonOutlinedStyle" id="0x7f03032b" />
    <public type="attr" name="materialIconButtonStyle" id="0x7f03032c" />
    <public type="attr" name="materialSearchBarStyle" id="0x7f03032d" />
    <public type="attr" name="materialSearchViewPrefixStyle" id="0x7f03032e" />
    <public type="attr" name="materialSearchViewStyle" id="0x7f03032f" />
    <public type="attr" name="materialSearchViewToolbarHeight" id="0x7f030330" />
    <public type="attr" name="materialSearchViewToolbarStyle" id="0x7f030331" />
    <public type="attr" name="materialSwitchStyle" id="0x7f030332" />
    <public type="attr" name="materialThemeOverlay" id="0x7f030333" />
    <public type="attr" name="materialTimePickerStyle" id="0x7f030334" />
    <public type="attr" name="materialTimePickerTheme" id="0x7f030335" />
    <public type="attr" name="materialTimePickerTitleStyle" id="0x7f030336" />
    <public type="attr" name="maxAcceleration" id="0x7f030337" />
    <public type="attr" name="maxActionInlineWidth" id="0x7f030338" />
    <public type="attr" name="maxButtonHeight" id="0x7f030339" />
    <public type="attr" name="maxCharacterCount" id="0x7f03033a" />
    <public type="attr" name="maxHeight" id="0x7f03033b" />
    <public type="attr" name="maxImageSize" id="0x7f03033c" />
    <public type="attr" name="maxLines" id="0x7f03033d" />
    <public type="attr" name="maxNumber" id="0x7f03033e" />
    <public type="attr" name="maxVelocity" id="0x7f03033f" />
    <public type="attr" name="maxWidth" id="0x7f030340" />
    <public type="attr" name="measureWithLargestChild" id="0x7f030341" />
    <public type="attr" name="menu" id="0x7f030342" />
    <public type="attr" name="menuAlignmentMode" id="0x7f030343" />
    <public type="attr" name="menuGravity" id="0x7f030344" />
    <public type="attr" name="methodName" id="0x7f030345" />
    <public type="attr" name="mimeType" id="0x7f030346" />
    <public type="attr" name="minHeight" id="0x7f030347" />
    <public type="attr" name="minHideDelay" id="0x7f030348" />
    <public type="attr" name="minSeparation" id="0x7f030349" />
    <public type="attr" name="minTouchTargetSize" id="0x7f03034a" />
    <public type="attr" name="minWidth" id="0x7f03034b" />
    <public type="attr" name="mock_diagonalsColor" id="0x7f03034c" />
    <public type="attr" name="mock_label" id="0x7f03034d" />
    <public type="attr" name="mock_labelBackgroundColor" id="0x7f03034e" />
    <public type="attr" name="mock_labelColor" id="0x7f03034f" />
    <public type="attr" name="mock_showDiagonals" id="0x7f030350" />
    <public type="attr" name="mock_showLabel" id="0x7f030351" />
    <public type="attr" name="motionDebug" id="0x7f030352" />
    <public type="attr" name="motionDurationExtraLong1" id="0x7f030353" />
    <public type="attr" name="motionDurationExtraLong2" id="0x7f030354" />
    <public type="attr" name="motionDurationExtraLong3" id="0x7f030355" />
    <public type="attr" name="motionDurationExtraLong4" id="0x7f030356" />
    <public type="attr" name="motionDurationLong1" id="0x7f030357" />
    <public type="attr" name="motionDurationLong2" id="0x7f030358" />
    <public type="attr" name="motionDurationLong3" id="0x7f030359" />
    <public type="attr" name="motionDurationLong4" id="0x7f03035a" />
    <public type="attr" name="motionDurationMedium1" id="0x7f03035b" />
    <public type="attr" name="motionDurationMedium2" id="0x7f03035c" />
    <public type="attr" name="motionDurationMedium3" id="0x7f03035d" />
    <public type="attr" name="motionDurationMedium4" id="0x7f03035e" />
    <public type="attr" name="motionDurationShort1" id="0x7f03035f" />
    <public type="attr" name="motionDurationShort2" id="0x7f030360" />
    <public type="attr" name="motionDurationShort3" id="0x7f030361" />
    <public type="attr" name="motionDurationShort4" id="0x7f030362" />
    <public type="attr" name="motionEasingAccelerated" id="0x7f030363" />
    <public type="attr" name="motionEasingDecelerated" id="0x7f030364" />
    <public type="attr" name="motionEasingEmphasized" id="0x7f030365" />
    <public type="attr" name="motionEasingEmphasizedAccelerateInterpolator" id="0x7f030366" />
    <public type="attr" name="motionEasingEmphasizedDecelerateInterpolator" id="0x7f030367" />
    <public type="attr" name="motionEasingEmphasizedInterpolator" id="0x7f030368" />
    <public type="attr" name="motionEasingLinear" id="0x7f030369" />
    <public type="attr" name="motionEasingLinearInterpolator" id="0x7f03036a" />
    <public type="attr" name="motionEasingStandard" id="0x7f03036b" />
    <public type="attr" name="motionEasingStandardAccelerateInterpolator" id="0x7f03036c" />
    <public type="attr" name="motionEasingStandardDecelerateInterpolator" id="0x7f03036d" />
    <public type="attr" name="motionEasingStandardInterpolator" id="0x7f03036e" />
    <public type="attr" name="motionEffect_alpha" id="0x7f03036f" />
    <public type="attr" name="motionEffect_end" id="0x7f030370" />
    <public type="attr" name="motionEffect_move" id="0x7f030371" />
    <public type="attr" name="motionEffect_start" id="0x7f030372" />
    <public type="attr" name="motionEffect_strict" id="0x7f030373" />
    <public type="attr" name="motionEffect_translationX" id="0x7f030374" />
    <public type="attr" name="motionEffect_translationY" id="0x7f030375" />
    <public type="attr" name="motionEffect_viewTransition" id="0x7f030376" />
    <public type="attr" name="motionInterpolator" id="0x7f030377" />
    <public type="attr" name="motionPath" id="0x7f030378" />
    <public type="attr" name="motionPathRotate" id="0x7f030379" />
    <public type="attr" name="motionProgress" id="0x7f03037a" />
    <public type="attr" name="motionStagger" id="0x7f03037b" />
    <public type="attr" name="motionTarget" id="0x7f03037c" />
    <public type="attr" name="motion_postLayoutCollision" id="0x7f03037d" />
    <public type="attr" name="motion_triggerOnCollision" id="0x7f03037e" />
    <public type="attr" name="moveWhenScrollAtTop" id="0x7f03037f" />
    <public type="attr" name="multiChoiceItemLayout" id="0x7f030380" />
    <public type="attr" name="navGraph" id="0x7f030381" />
    <public type="attr" name="navigationContentDescription" id="0x7f030382" />
    <public type="attr" name="navigationIcon" id="0x7f030383" />
    <public type="attr" name="navigationIconTint" id="0x7f030384" />
    <public type="attr" name="navigationMode" id="0x7f030385" />
    <public type="attr" name="navigationRailStyle" id="0x7f030386" />
    <public type="attr" name="navigationViewStyle" id="0x7f030387" />
    <public type="attr" name="nestedScrollFlags" id="0x7f030388" />
    <public type="attr" name="nestedScrollViewStyle" id="0x7f030389" />
    <public type="attr" name="nestedScrollable" id="0x7f03038a" />
    <public type="attr" name="nullable" id="0x7f03038b" />
    <public type="attr" name="number" id="0x7f03038c" />
    <public type="attr" name="numericModifiers" id="0x7f03038d" />
    <public type="attr" name="offsetAlignmentMode" id="0x7f03038e" />
    <public type="attr" name="onCross" id="0x7f03038f" />
    <public type="attr" name="onHide" id="0x7f030390" />
    <public type="attr" name="onNegativeCross" id="0x7f030391" />
    <public type="attr" name="onPositiveCross" id="0x7f030392" />
    <public type="attr" name="onShow" id="0x7f030393" />
    <public type="attr" name="onStateTransition" id="0x7f030394" />
    <public type="attr" name="onTouchUp" id="0x7f030395" />
    <public type="attr" name="overlapAnchor" id="0x7f030396" />
    <public type="attr" name="overlay" id="0x7f030397" />
    <public type="attr" name="paddingBottomNoButtons" id="0x7f030398" />
    <public type="attr" name="paddingBottomSystemWindowInsets" id="0x7f030399" />
    <public type="attr" name="paddingEnd" id="0x7f03039a" />
    <public type="attr" name="paddingLeftSystemWindowInsets" id="0x7f03039b" />
    <public type="attr" name="paddingRightSystemWindowInsets" id="0x7f03039c" />
    <public type="attr" name="paddingStart" id="0x7f03039d" />
    <public type="attr" name="paddingStartSystemWindowInsets" id="0x7f03039e" />
    <public type="attr" name="paddingTopNoTitle" id="0x7f03039f" />
    <public type="attr" name="paddingTopSystemWindowInsets" id="0x7f0303a0" />
    <public type="attr" name="panelBackground" id="0x7f0303a1" />
    <public type="attr" name="panelMenuListTheme" id="0x7f0303a2" />
    <public type="attr" name="panelMenuListWidth" id="0x7f0303a3" />
    <public type="attr" name="passwordToggleContentDescription" id="0x7f0303a4" />
    <public type="attr" name="passwordToggleDrawable" id="0x7f0303a5" />
    <public type="attr" name="passwordToggleEnabled" id="0x7f0303a6" />
    <public type="attr" name="passwordToggleTint" id="0x7f0303a7" />
    <public type="attr" name="passwordToggleTintMode" id="0x7f0303a8" />
    <public type="attr" name="pathMotionArc" id="0x7f0303a9" />
    <public type="attr" name="path_percent" id="0x7f0303aa" />
    <public type="attr" name="percentHeight" id="0x7f0303ab" />
    <public type="attr" name="percentWidth" id="0x7f0303ac" />
    <public type="attr" name="percentX" id="0x7f0303ad" />
    <public type="attr" name="percentY" id="0x7f0303ae" />
    <public type="attr" name="perpendicularPath_percent" id="0x7f0303af" />
    <public type="attr" name="pivotAnchor" id="0x7f0303b0" />
    <public type="attr" name="placeholderText" id="0x7f0303b1" />
    <public type="attr" name="placeholderTextAppearance" id="0x7f0303b2" />
    <public type="attr" name="placeholderTextColor" id="0x7f0303b3" />
    <public type="attr" name="placeholder_emptyVisibility" id="0x7f0303b4" />
    <public type="attr" name="polarRelativeTo" id="0x7f0303b5" />
    <public type="attr" name="popEnterAnim" id="0x7f0303b6" />
    <public type="attr" name="popExitAnim" id="0x7f0303b7" />
    <public type="attr" name="popUpTo" id="0x7f0303b8" />
    <public type="attr" name="popUpToInclusive" id="0x7f0303b9" />
    <public type="attr" name="popUpToSaveState" id="0x7f0303ba" />
    <public type="attr" name="popupMenuBackground" id="0x7f0303bb" />
    <public type="attr" name="popupMenuStyle" id="0x7f0303bc" />
    <public type="attr" name="popupTheme" id="0x7f0303bd" />
    <public type="attr" name="popupWindowStyle" id="0x7f0303be" />
    <public type="attr" name="postSplashScreenTheme" id="0x7f0303bf" />
    <public type="attr" name="prefixText" id="0x7f0303c0" />
    <public type="attr" name="prefixTextAppearance" id="0x7f0303c1" />
    <public type="attr" name="prefixTextColor" id="0x7f0303c2" />
    <public type="attr" name="preserveIconSpacing" id="0x7f0303c3" />
    <public type="attr" name="pressedTranslationZ" id="0x7f0303c4" />
    <public type="attr" name="progressBarPadding" id="0x7f0303c5" />
    <public type="attr" name="progressBarStyle" id="0x7f0303c6" />
    <public type="attr" name="quantizeMotionInterpolator" id="0x7f0303c7" />
    <public type="attr" name="quantizeMotionPhase" id="0x7f0303c8" />
    <public type="attr" name="quantizeMotionSteps" id="0x7f0303c9" />
    <public type="attr" name="queryBackground" id="0x7f0303ca" />
    <public type="attr" name="queryHint" id="0x7f0303cb" />
    <public type="attr" name="queryPatterns" id="0x7f0303cc" />
    <public type="attr" name="radioButtonStyle" id="0x7f0303cd" />
    <public type="attr" name="rangeFillColor" id="0x7f0303ce" />
    <public type="attr" name="ratingBarStyle" id="0x7f0303cf" />
    <public type="attr" name="ratingBarStyleIndicator" id="0x7f0303d0" />
    <public type="attr" name="ratingBarStyleSmall" id="0x7f0303d1" />
    <public type="attr" name="reactiveGuide_animateChange" id="0x7f0303d2" />
    <public type="attr" name="reactiveGuide_applyToAllConstraintSets" id="0x7f0303d3" />
    <public type="attr" name="reactiveGuide_applyToConstraintSet" id="0x7f0303d4" />
    <public type="attr" name="reactiveGuide_valueId" id="0x7f0303d5" />
    <public type="attr" name="recyclerViewStyle" id="0x7f0303d6" />
    <public type="attr" name="region_heightLessThan" id="0x7f0303d7" />
    <public type="attr" name="region_heightMoreThan" id="0x7f0303d8" />
    <public type="attr" name="region_widthLessThan" id="0x7f0303d9" />
    <public type="attr" name="region_widthMoreThan" id="0x7f0303da" />
    <public type="attr" name="removeEmbeddedFabElevation" id="0x7f0303db" />
    <public type="attr" name="restoreState" id="0x7f0303dc" />
    <public type="attr" name="reverseLayout" id="0x7f0303dd" />
    <public type="attr" name="rippleColor" id="0x7f0303de" />
    <public type="attr" name="rotationCenterId" id="0x7f0303df" />
    <public type="attr" name="round" id="0x7f0303e0" />
    <public type="attr" name="roundPercent" id="0x7f0303e1" />
    <public type="attr" name="route" id="0x7f0303e2" />
    <public type="attr" name="saturation" id="0x7f0303e3" />
    <public type="attr" name="scaleFromTextSize" id="0x7f0303e4" />
    <public type="attr" name="scopeUris" id="0x7f0303e5" />
    <public type="attr" name="scrimAnimationDuration" id="0x7f0303e6" />
    <public type="attr" name="scrimBackground" id="0x7f0303e7" />
    <public type="attr" name="scrimVisibleHeightTrigger" id="0x7f0303e8" />
    <public type="attr" name="searchHintIcon" id="0x7f0303e9" />
    <public type="attr" name="searchIcon" id="0x7f0303ea" />
    <public type="attr" name="searchPrefixText" id="0x7f0303eb" />
    <public type="attr" name="searchViewStyle" id="0x7f0303ec" />
    <public type="attr" name="seekBarStyle" id="0x7f0303ed" />
    <public type="attr" name="selectableItemBackground" id="0x7f0303ee" />
    <public type="attr" name="selectableItemBackgroundBorderless" id="0x7f0303ef" />
    <public type="attr" name="selectionRequired" id="0x7f0303f0" />
    <public type="attr" name="selectorSize" id="0x7f0303f1" />
    <public type="attr" name="setsTag" id="0x7f0303f2" />
    <public type="attr" name="shapeAppearance" id="0x7f0303f3" />
    <public type="attr" name="shapeAppearanceCornerExtraLarge" id="0x7f0303f4" />
    <public type="attr" name="shapeAppearanceCornerExtraSmall" id="0x7f0303f5" />
    <public type="attr" name="shapeAppearanceCornerLarge" id="0x7f0303f6" />
    <public type="attr" name="shapeAppearanceCornerMedium" id="0x7f0303f7" />
    <public type="attr" name="shapeAppearanceCornerSmall" id="0x7f0303f8" />
    <public type="attr" name="shapeAppearanceLargeComponent" id="0x7f0303f9" />
    <public type="attr" name="shapeAppearanceMediumComponent" id="0x7f0303fa" />
    <public type="attr" name="shapeAppearanceOverlay" id="0x7f0303fb" />
    <public type="attr" name="shapeAppearanceSmallComponent" id="0x7f0303fc" />
    <public type="attr" name="shapeCornerFamily" id="0x7f0303fd" />
    <public type="attr" name="shortcutMatchRequired" id="0x7f0303fe" />
    <public type="attr" name="shouldRemoveExpandedCorners" id="0x7f0303ff" />
    <public type="attr" name="showAnimationBehavior" id="0x7f030400" />
    <public type="attr" name="showAsAction" id="0x7f030401" />
    <public type="attr" name="showDelay" id="0x7f030402" />
    <public type="attr" name="showDividers" id="0x7f030403" />
    <public type="attr" name="showMarker" id="0x7f030404" />
    <public type="attr" name="showMotionSpec" id="0x7f030405" />
    <public type="attr" name="showPaths" id="0x7f030406" />
    <public type="attr" name="showText" id="0x7f030407" />
    <public type="attr" name="showTitle" id="0x7f030408" />
    <public type="attr" name="shrinkMotionSpec" id="0x7f030409" />
    <public type="attr" name="sideSheetDialogTheme" id="0x7f03040a" />
    <public type="attr" name="sideSheetModalStyle" id="0x7f03040b" />
    <public type="attr" name="simpleItemLayout" id="0x7f03040c" />
    <public type="attr" name="simpleItemSelectedColor" id="0x7f03040d" />
    <public type="attr" name="simpleItemSelectedRippleColor" id="0x7f03040e" />
    <public type="attr" name="simpleItems" id="0x7f03040f" />
    <public type="attr" name="singleChoiceItemLayout" id="0x7f030410" />
    <public type="attr" name="singleLine" id="0x7f030411" />
    <public type="attr" name="singleSelection" id="0x7f030412" />
    <public type="attr" name="sizePercent" id="0x7f030413" />
    <public type="attr" name="sliderStyle" id="0x7f030414" />
    <public type="attr" name="snackbarButtonStyle" id="0x7f030415" />
    <public type="attr" name="snackbarStyle" id="0x7f030416" />
    <public type="attr" name="snackbarTextViewStyle" id="0x7f030417" />
    <public type="attr" name="spanCount" id="0x7f030418" />
    <public type="attr" name="spinBars" id="0x7f030419" />
    <public type="attr" name="spinnerDropDownItemStyle" id="0x7f03041a" />
    <public type="attr" name="spinnerStyle" id="0x7f03041b" />
    <public type="attr" name="splashScreenIconSize" id="0x7f03041c" />
    <public type="attr" name="splitTrack" id="0x7f03041d" />
    <public type="attr" name="springBoundary" id="0x7f03041e" />
    <public type="attr" name="springDamping" id="0x7f03041f" />
    <public type="attr" name="springMass" id="0x7f030420" />
    <public type="attr" name="springStiffness" id="0x7f030421" />
    <public type="attr" name="springStopThreshold" id="0x7f030422" />
    <public type="attr" name="srcCompat" id="0x7f030423" />
    <public type="attr" name="stackFromEnd" id="0x7f030424" />
    <public type="attr" name="staggered" id="0x7f030425" />
    <public type="attr" name="startDestination" id="0x7f030426" />
    <public type="attr" name="startIconCheckable" id="0x7f030427" />
    <public type="attr" name="startIconContentDescription" id="0x7f030428" />
    <public type="attr" name="startIconDrawable" id="0x7f030429" />
    <public type="attr" name="startIconMinSize" id="0x7f03042a" />
    <public type="attr" name="startIconScaleType" id="0x7f03042b" />
    <public type="attr" name="startIconTint" id="0x7f03042c" />
    <public type="attr" name="startIconTintMode" id="0x7f03042d" />
    <public type="attr" name="state_above_anchor" id="0x7f03042e" />
    <public type="attr" name="state_collapsed" id="0x7f03042f" />
    <public type="attr" name="state_collapsible" id="0x7f030430" />
    <public type="attr" name="state_dragged" id="0x7f030431" />
    <public type="attr" name="state_error" id="0x7f030432" />
    <public type="attr" name="state_indeterminate" id="0x7f030433" />
    <public type="attr" name="state_liftable" id="0x7f030434" />
    <public type="attr" name="state_lifted" id="0x7f030435" />
    <public type="attr" name="state_with_icon" id="0x7f030436" />
    <public type="attr" name="statusBarBackground" id="0x7f030437" />
    <public type="attr" name="statusBarForeground" id="0x7f030438" />
    <public type="attr" name="statusBarScrim" id="0x7f030439" />
    <public type="attr" name="strokeColor" id="0x7f03043a" />
    <public type="attr" name="strokeWidth" id="0x7f03043b" />
    <public type="attr" name="subMenuArrow" id="0x7f03043c" />
    <public type="attr" name="subheaderColor" id="0x7f03043d" />
    <public type="attr" name="subheaderInsetEnd" id="0x7f03043e" />
    <public type="attr" name="subheaderInsetStart" id="0x7f03043f" />
    <public type="attr" name="subheaderTextAppearance" id="0x7f030440" />
    <public type="attr" name="submitBackground" id="0x7f030441" />
    <public type="attr" name="subtitle" id="0x7f030442" />
    <public type="attr" name="subtitleCentered" id="0x7f030443" />
    <public type="attr" name="subtitleTextAppearance" id="0x7f030444" />
    <public type="attr" name="subtitleTextColor" id="0x7f030445" />
    <public type="attr" name="subtitleTextStyle" id="0x7f030446" />
    <public type="attr" name="suffixText" id="0x7f030447" />
    <public type="attr" name="suffixTextAppearance" id="0x7f030448" />
    <public type="attr" name="suffixTextColor" id="0x7f030449" />
    <public type="attr" name="suggestionRowLayout" id="0x7f03044a" />
    <public type="attr" name="swipeRefreshLayoutProgressSpinnerBackgroundColor" id="0x7f03044b" />
    <public type="attr" name="switchMinWidth" id="0x7f03044c" />
    <public type="attr" name="switchPadding" id="0x7f03044d" />
    <public type="attr" name="switchStyle" id="0x7f03044e" />
    <public type="attr" name="switchTextAppearance" id="0x7f03044f" />
    <public type="attr" name="tabBackground" id="0x7f030450" />
    <public type="attr" name="tabContentStart" id="0x7f030451" />
    <public type="attr" name="tabGravity" id="0x7f030452" />
    <public type="attr" name="tabIconTint" id="0x7f030453" />
    <public type="attr" name="tabIconTintMode" id="0x7f030454" />
    <public type="attr" name="tabIndicator" id="0x7f030455" />
    <public type="attr" name="tabIndicatorAnimationDuration" id="0x7f030456" />
    <public type="attr" name="tabIndicatorAnimationMode" id="0x7f030457" />
    <public type="attr" name="tabIndicatorColor" id="0x7f030458" />
    <public type="attr" name="tabIndicatorFullWidth" id="0x7f030459" />
    <public type="attr" name="tabIndicatorGravity" id="0x7f03045a" />
    <public type="attr" name="tabIndicatorHeight" id="0x7f03045b" />
    <public type="attr" name="tabInlineLabel" id="0x7f03045c" />
    <public type="attr" name="tabMaxWidth" id="0x7f03045d" />
    <public type="attr" name="tabMinWidth" id="0x7f03045e" />
    <public type="attr" name="tabMode" id="0x7f03045f" />
    <public type="attr" name="tabPadding" id="0x7f030460" />
    <public type="attr" name="tabPaddingBottom" id="0x7f030461" />
    <public type="attr" name="tabPaddingEnd" id="0x7f030462" />
    <public type="attr" name="tabPaddingStart" id="0x7f030463" />
    <public type="attr" name="tabPaddingTop" id="0x7f030464" />
    <public type="attr" name="tabRippleColor" id="0x7f030465" />
    <public type="attr" name="tabSecondaryStyle" id="0x7f030466" />
    <public type="attr" name="tabSelectedTextAppearance" id="0x7f030467" />
    <public type="attr" name="tabSelectedTextColor" id="0x7f030468" />
    <public type="attr" name="tabStyle" id="0x7f030469" />
    <public type="attr" name="tabTextAppearance" id="0x7f03046a" />
    <public type="attr" name="tabTextColor" id="0x7f03046b" />
    <public type="attr" name="tabUnboundedRipple" id="0x7f03046c" />
    <public type="attr" name="targetId" id="0x7f03046d" />
    <public type="attr" name="targetPackage" id="0x7f03046e" />
    <public type="attr" name="telltales_tailColor" id="0x7f03046f" />
    <public type="attr" name="telltales_tailScale" id="0x7f030470" />
    <public type="attr" name="telltales_velocityMode" id="0x7f030471" />
    <public type="attr" name="textAllCaps" id="0x7f030472" />
    <public type="attr" name="textAppearanceBody1" id="0x7f030473" />
    <public type="attr" name="textAppearanceBody2" id="0x7f030474" />
    <public type="attr" name="textAppearanceBodyLarge" id="0x7f030475" />
    <public type="attr" name="textAppearanceBodyMedium" id="0x7f030476" />
    <public type="attr" name="textAppearanceBodySmall" id="0x7f030477" />
    <public type="attr" name="textAppearanceButton" id="0x7f030478" />
    <public type="attr" name="textAppearanceCaption" id="0x7f030479" />
    <public type="attr" name="textAppearanceDisplayLarge" id="0x7f03047a" />
    <public type="attr" name="textAppearanceDisplayMedium" id="0x7f03047b" />
    <public type="attr" name="textAppearanceDisplaySmall" id="0x7f03047c" />
    <public type="attr" name="textAppearanceHeadline1" id="0x7f03047d" />
    <public type="attr" name="textAppearanceHeadline2" id="0x7f03047e" />
    <public type="attr" name="textAppearanceHeadline3" id="0x7f03047f" />
    <public type="attr" name="textAppearanceHeadline4" id="0x7f030480" />
    <public type="attr" name="textAppearanceHeadline5" id="0x7f030481" />
    <public type="attr" name="textAppearanceHeadline6" id="0x7f030482" />
    <public type="attr" name="textAppearanceHeadlineLarge" id="0x7f030483" />
    <public type="attr" name="textAppearanceHeadlineMedium" id="0x7f030484" />
    <public type="attr" name="textAppearanceHeadlineSmall" id="0x7f030485" />
    <public type="attr" name="textAppearanceLabelLarge" id="0x7f030486" />
    <public type="attr" name="textAppearanceLabelMedium" id="0x7f030487" />
    <public type="attr" name="textAppearanceLabelSmall" id="0x7f030488" />
    <public type="attr" name="textAppearanceLargePopupMenu" id="0x7f030489" />
    <public type="attr" name="textAppearanceLineHeightEnabled" id="0x7f03048a" />
    <public type="attr" name="textAppearanceListItem" id="0x7f03048b" />
    <public type="attr" name="textAppearanceListItemSecondary" id="0x7f03048c" />
    <public type="attr" name="textAppearanceListItemSmall" id="0x7f03048d" />
    <public type="attr" name="textAppearanceOverline" id="0x7f03048e" />
    <public type="attr" name="textAppearancePopupMenuHeader" id="0x7f03048f" />
    <public type="attr" name="textAppearanceSearchResultSubtitle" id="0x7f030490" />
    <public type="attr" name="textAppearanceSearchResultTitle" id="0x7f030491" />
    <public type="attr" name="textAppearanceSmallPopupMenu" id="0x7f030492" />
    <public type="attr" name="textAppearanceSubtitle1" id="0x7f030493" />
    <public type="attr" name="textAppearanceSubtitle2" id="0x7f030494" />
    <public type="attr" name="textAppearanceTitleLarge" id="0x7f030495" />
    <public type="attr" name="textAppearanceTitleMedium" id="0x7f030496" />
    <public type="attr" name="textAppearanceTitleSmall" id="0x7f030497" />
    <public type="attr" name="textBackground" id="0x7f030498" />
    <public type="attr" name="textBackgroundPanX" id="0x7f030499" />
    <public type="attr" name="textBackgroundPanY" id="0x7f03049a" />
    <public type="attr" name="textBackgroundRotate" id="0x7f03049b" />
    <public type="attr" name="textBackgroundZoom" id="0x7f03049c" />
    <public type="attr" name="textColorAlertDialogListItem" id="0x7f03049d" />
    <public type="attr" name="textColorSearchUrl" id="0x7f03049e" />
    <public type="attr" name="textEndPadding" id="0x7f03049f" />
    <public type="attr" name="textFillColor" id="0x7f0304a0" />
    <public type="attr" name="textInputFilledDenseStyle" id="0x7f0304a1" />
    <public type="attr" name="textInputFilledExposedDropdownMenuStyle" id="0x7f0304a2" />
    <public type="attr" name="textInputFilledStyle" id="0x7f0304a3" />
    <public type="attr" name="textInputLayoutFocusedRectEnabled" id="0x7f0304a4" />
    <public type="attr" name="textInputOutlinedDenseStyle" id="0x7f0304a5" />
    <public type="attr" name="textInputOutlinedExposedDropdownMenuStyle" id="0x7f0304a6" />
    <public type="attr" name="textInputOutlinedStyle" id="0x7f0304a7" />
    <public type="attr" name="textInputStyle" id="0x7f0304a8" />
    <public type="attr" name="textLocale" id="0x7f0304a9" />
    <public type="attr" name="textOutlineColor" id="0x7f0304aa" />
    <public type="attr" name="textOutlineThickness" id="0x7f0304ab" />
    <public type="attr" name="textPanX" id="0x7f0304ac" />
    <public type="attr" name="textPanY" id="0x7f0304ad" />
    <public type="attr" name="textStartPadding" id="0x7f0304ae" />
    <public type="attr" name="textureBlurFactor" id="0x7f0304af" />
    <public type="attr" name="textureEffect" id="0x7f0304b0" />
    <public type="attr" name="textureHeight" id="0x7f0304b1" />
    <public type="attr" name="textureWidth" id="0x7f0304b2" />
    <public type="attr" name="theme" id="0x7f0304b3" />
    <public type="attr" name="thickness" id="0x7f0304b4" />
    <public type="attr" name="thumbColor" id="0x7f0304b5" />
    <public type="attr" name="thumbElevation" id="0x7f0304b6" />
    <public type="attr" name="thumbHeight" id="0x7f0304b7" />
    <public type="attr" name="thumbIcon" id="0x7f0304b8" />
    <public type="attr" name="thumbIconSize" id="0x7f0304b9" />
    <public type="attr" name="thumbIconTint" id="0x7f0304ba" />
    <public type="attr" name="thumbIconTintMode" id="0x7f0304bb" />
    <public type="attr" name="thumbRadius" id="0x7f0304bc" />
    <public type="attr" name="thumbStrokeColor" id="0x7f0304bd" />
    <public type="attr" name="thumbStrokeWidth" id="0x7f0304be" />
    <public type="attr" name="thumbTextPadding" id="0x7f0304bf" />
    <public type="attr" name="thumbTint" id="0x7f0304c0" />
    <public type="attr" name="thumbTintMode" id="0x7f0304c1" />
    <public type="attr" name="thumbTrackGapSize" id="0x7f0304c2" />
    <public type="attr" name="thumbWidth" id="0x7f0304c3" />
    <public type="attr" name="tickColor" id="0x7f0304c4" />
    <public type="attr" name="tickColorActive" id="0x7f0304c5" />
    <public type="attr" name="tickColorInactive" id="0x7f0304c6" />
    <public type="attr" name="tickMark" id="0x7f0304c7" />
    <public type="attr" name="tickMarkTint" id="0x7f0304c8" />
    <public type="attr" name="tickMarkTintMode" id="0x7f0304c9" />
    <public type="attr" name="tickRadiusActive" id="0x7f0304ca" />
    <public type="attr" name="tickRadiusInactive" id="0x7f0304cb" />
    <public type="attr" name="tickVisible" id="0x7f0304cc" />
    <public type="attr" name="tint" id="0x7f0304cd" />
    <public type="attr" name="tintMode" id="0x7f0304ce" />
    <public type="attr" name="tintNavigationIcon" id="0x7f0304cf" />
    <public type="attr" name="title" id="0x7f0304d0" />
    <public type="attr" name="titleCentered" id="0x7f0304d1" />
    <public type="attr" name="titleCollapseMode" id="0x7f0304d2" />
    <public type="attr" name="titleEnabled" id="0x7f0304d3" />
    <public type="attr" name="titleMargin" id="0x7f0304d4" />
    <public type="attr" name="titleMarginBottom" id="0x7f0304d5" />
    <public type="attr" name="titleMarginEnd" id="0x7f0304d6" />
    <public type="attr" name="titleMarginStart" id="0x7f0304d7" />
    <public type="attr" name="titleMarginTop" id="0x7f0304d8" />
    <public type="attr" name="titleMargins" id="0x7f0304d9" />
    <public type="attr" name="titlePositionInterpolator" id="0x7f0304da" />
    <public type="attr" name="titleTextAppearance" id="0x7f0304db" />
    <public type="attr" name="titleTextColor" id="0x7f0304dc" />
    <public type="attr" name="titleTextEllipsize" id="0x7f0304dd" />
    <public type="attr" name="titleTextStyle" id="0x7f0304de" />
    <public type="attr" name="toggleCheckedStateOnClick" id="0x7f0304df" />
    <public type="attr" name="toolbarId" id="0x7f0304e0" />
    <public type="attr" name="toolbarNavigationButtonStyle" id="0x7f0304e1" />
    <public type="attr" name="toolbarStyle" id="0x7f0304e2" />
    <public type="attr" name="toolbarSurfaceStyle" id="0x7f0304e3" />
    <public type="attr" name="tooltipForegroundColor" id="0x7f0304e4" />
    <public type="attr" name="tooltipFrameBackground" id="0x7f0304e5" />
    <public type="attr" name="tooltipStyle" id="0x7f0304e6" />
    <public type="attr" name="tooltipText" id="0x7f0304e7" />
    <public type="attr" name="topInsetScrimEnabled" id="0x7f0304e8" />
    <public type="attr" name="touchAnchorId" id="0x7f0304e9" />
    <public type="attr" name="touchAnchorSide" id="0x7f0304ea" />
    <public type="attr" name="touchRegionId" id="0x7f0304eb" />
    <public type="attr" name="track" id="0x7f0304ec" />
    <public type="attr" name="trackColor" id="0x7f0304ed" />
    <public type="attr" name="trackColorActive" id="0x7f0304ee" />
    <public type="attr" name="trackColorInactive" id="0x7f0304ef" />
    <public type="attr" name="trackCornerRadius" id="0x7f0304f0" />
    <public type="attr" name="trackDecoration" id="0x7f0304f1" />
    <public type="attr" name="trackDecorationTint" id="0x7f0304f2" />
    <public type="attr" name="trackDecorationTintMode" id="0x7f0304f3" />
    <public type="attr" name="trackHeight" id="0x7f0304f4" />
    <public type="attr" name="trackInsideCornerSize" id="0x7f0304f5" />
    <public type="attr" name="trackStopIndicatorSize" id="0x7f0304f6" />
    <public type="attr" name="trackThickness" id="0x7f0304f7" />
    <public type="attr" name="trackTint" id="0x7f0304f8" />
    <public type="attr" name="trackTintMode" id="0x7f0304f9" />
    <public type="attr" name="transformPivotTarget" id="0x7f0304fa" />
    <public type="attr" name="transitionDisable" id="0x7f0304fb" />
    <public type="attr" name="transitionEasing" id="0x7f0304fc" />
    <public type="attr" name="transitionFlags" id="0x7f0304fd" />
    <public type="attr" name="transitionPathRotate" id="0x7f0304fe" />
    <public type="attr" name="transitionShapeAppearance" id="0x7f0304ff" />
    <public type="attr" name="triggerId" id="0x7f030500" />
    <public type="attr" name="triggerReceiver" id="0x7f030501" />
    <public type="attr" name="triggerSlack" id="0x7f030502" />
    <public type="attr" name="ttcIndex" id="0x7f030503" />
    <public type="attr" name="upDuration" id="0x7f030504" />
    <public type="attr" name="uri" id="0x7f030505" />
    <public type="attr" name="useCompatPadding" id="0x7f030506" />
    <public type="attr" name="useDrawerArrowDrawable" id="0x7f030507" />
    <public type="attr" name="useMaterialThemeColors" id="0x7f030508" />
    <public type="attr" name="values" id="0x7f030509" />
    <public type="attr" name="verticalOffset" id="0x7f03050a" />
    <public type="attr" name="verticalOffsetWithText" id="0x7f03050b" />
    <public type="attr" name="viewInflaterClass" id="0x7f03050c" />
    <public type="attr" name="viewTransitionMode" id="0x7f03050d" />
    <public type="attr" name="viewTransitionOnCross" id="0x7f03050e" />
    <public type="attr" name="viewTransitionOnNegativeCross" id="0x7f03050f" />
    <public type="attr" name="viewTransitionOnPositiveCross" id="0x7f030510" />
    <public type="attr" name="visibilityMode" id="0x7f030511" />
    <public type="attr" name="voiceIcon" id="0x7f030512" />
    <public type="attr" name="warmth" id="0x7f030513" />
    <public type="attr" name="waveDecay" id="0x7f030514" />
    <public type="attr" name="waveOffset" id="0x7f030515" />
    <public type="attr" name="wavePeriod" id="0x7f030516" />
    <public type="attr" name="wavePhase" id="0x7f030517" />
    <public type="attr" name="waveShape" id="0x7f030518" />
    <public type="attr" name="waveVariesBy" id="0x7f030519" />
    <public type="attr" name="windowActionBar" id="0x7f03051a" />
    <public type="attr" name="windowActionBarOverlay" id="0x7f03051b" />
    <public type="attr" name="windowActionModeOverlay" id="0x7f03051c" />
    <public type="attr" name="windowFixedHeightMajor" id="0x7f03051d" />
    <public type="attr" name="windowFixedHeightMinor" id="0x7f03051e" />
    <public type="attr" name="windowFixedWidthMajor" id="0x7f03051f" />
    <public type="attr" name="windowFixedWidthMinor" id="0x7f030520" />
    <public type="attr" name="windowMinWidthMajor" id="0x7f030521" />
    <public type="attr" name="windowMinWidthMinor" id="0x7f030522" />
    <public type="attr" name="windowNoTitle" id="0x7f030523" />
    <public type="attr" name="windowSplashScreenAnimatedIcon" id="0x7f030524" />
    <public type="attr" name="windowSplashScreenAnimationDuration" id="0x7f030525" />
    <public type="attr" name="windowSplashScreenBackground" id="0x7f030526" />
    <public type="attr" name="windowSplashScreenIconBackgroundColor" id="0x7f030527" />
    <public type="attr" name="yearSelectedStyle" id="0x7f030528" />
    <public type="attr" name="yearStyle" id="0x7f030529" />
    <public type="attr" name="yearTodayStyle" id="0x7f03052a" />
    <public type="bool" name="abc_action_bar_embed_tabs" id="0x7f040000" />
    <public type="bool" name="abc_config_actionMenuItemAllCaps" id="0x7f040001" />
    <public type="bool" name="enable_system_alarm_service_default" id="0x7f040002" />
    <public type="bool" name="enable_system_foreground_service_default" id="0x7f040003" />
    <public type="bool" name="enable_system_job_service_default" id="0x7f040004" />
    <public type="bool" name="workmanager_test_configuration" id="0x7f040006" />
    <public type="color" name="abc_background_cache_hint_selector_material_dark" id="0x7f050000" />
    <public type="color" name="abc_background_cache_hint_selector_material_light" id="0x7f050001" />
    <public type="color" name="abc_btn_colored_borderless_text_material" id="0x7f050002" />
    <public type="color" name="abc_btn_colored_text_material" id="0x7f050003" />
    <public type="color" name="abc_color_highlight_material" id="0x7f050004" />
    <public type="color" name="abc_decor_view_status_guard" id="0x7f050005" />
    <public type="color" name="abc_decor_view_status_guard_light" id="0x7f050006" />
    <public type="color" name="abc_hint_foreground_material_dark" id="0x7f050007" />
    <public type="color" name="abc_hint_foreground_material_light" id="0x7f050008" />
    <public type="color" name="abc_primary_text_disable_only_material_dark" id="0x7f050009" />
    <public type="color" name="abc_primary_text_disable_only_material_light" id="0x7f05000a" />
    <public type="color" name="abc_primary_text_material_dark" id="0x7f05000b" />
    <public type="color" name="abc_primary_text_material_light" id="0x7f05000c" />
    <public type="color" name="abc_search_url_text" id="0x7f05000d" />
    <public type="color" name="abc_search_url_text_normal" id="0x7f05000e" />
    <public type="color" name="abc_search_url_text_pressed" id="0x7f05000f" />
    <public type="color" name="abc_search_url_text_selected" id="0x7f050010" />
    <public type="color" name="abc_secondary_text_material_dark" id="0x7f050011" />
    <public type="color" name="abc_secondary_text_material_light" id="0x7f050012" />
    <public type="color" name="abc_tint_btn_checkable" id="0x7f050013" />
    <public type="color" name="abc_tint_default" id="0x7f050014" />
    <public type="color" name="abc_tint_edittext" id="0x7f050015" />
    <public type="color" name="abc_tint_seek_thumb" id="0x7f050016" />
    <public type="color" name="abc_tint_spinner" id="0x7f050017" />
    <public type="color" name="abc_tint_switch_track" id="0x7f050018" />
    <public type="color" name="accent_material_dark" id="0x7f050019" />
    <public type="color" name="accent_material_light" id="0x7f05001a" />
    <public type="color" name="androidx_core_ripple_material_light" id="0x7f05001b" />
    <public type="color" name="androidx_core_secondary_text_default_material_light" id="0x7f05001c" />
    <public type="color" name="background_floating_material_dark" id="0x7f05001d" />
    <public type="color" name="background_floating_material_light" id="0x7f05001e" />
    <public type="color" name="background_material_dark" id="0x7f05001f" />
    <public type="color" name="background_material_light" id="0x7f050020" />
    <public type="color" name="black" id="0x7f050021" />
    <public type="color" name="bright_foreground_disabled_material_dark" id="0x7f050022" />
    <public type="color" name="bright_foreground_disabled_material_light" id="0x7f050023" />
    <public type="color" name="bright_foreground_material_dark" id="0x7f050026" />
    <public type="color" name="bright_foreground_material_light" id="0x7f050027" />
    <public type="color" name="browser_actions_bg_grey" id="0x7f050028" />
    <public type="color" name="browser_actions_divider_color" id="0x7f050029" />
    <public type="color" name="browser_actions_text_color" id="0x7f05002a" />
    <public type="color" name="browser_actions_title_color" id="0x7f05002b" />
    <public type="color" name="button_material_dark" id="0x7f05002c" />
    <public type="color" name="button_material_light" id="0x7f05002d" />
    <public type="color" name="call_notification_answer_color" id="0x7f05002e" />
    <public type="color" name="call_notification_decline_color" id="0x7f05002f" />
    <public type="color" name="cardview_dark_background" id="0x7f050030" />
    <public type="color" name="cardview_light_background" id="0x7f050031" />
    <public type="color" name="cardview_shadow_end_color" id="0x7f050032" />
    <public type="color" name="cardview_shadow_start_color" id="0x7f050033" />
    <public type="color" name="colorAccent" id="0x7f050034" />
    <public type="color" name="colorPrimary" id="0x7f050035" />
    <public type="color" name="colorPrimaryDark" id="0x7f050036" />
    <public type="color" name="colorText" id="0x7f050037" />
    <public type="color" name="colorWhite" id="0x7f050038" />
    <public type="color" name="common_google_signin_btn_text_dark" id="0x7f050039" />
    <public type="color" name="common_google_signin_btn_text_dark_default" id="0x7f05003a" />
    <public type="color" name="common_google_signin_btn_text_dark_disabled" id="0x7f05003b" />
    <public type="color" name="common_google_signin_btn_text_dark_focused" id="0x7f05003c" />
    <public type="color" name="common_google_signin_btn_text_dark_pressed" id="0x7f05003d" />
    <public type="color" name="common_google_signin_btn_text_light" id="0x7f05003e" />
    <public type="color" name="common_google_signin_btn_text_light_default" id="0x7f05003f" />
    <public type="color" name="common_google_signin_btn_text_light_disabled" id="0x7f050040" />
    <public type="color" name="common_google_signin_btn_text_light_focused" id="0x7f050041" />
    <public type="color" name="common_google_signin_btn_text_light_pressed" id="0x7f050042" />
    <public type="color" name="common_google_signin_btn_tint" id="0x7f050043" />
    <public type="color" name="date_time_color" id="0x7f050044" />
    <public type="color" name="design_box_stroke_color" id="0x7f050046" />
    <public type="color" name="design_dark_default_color_background" id="0x7f050047" />
    <public type="color" name="design_dark_default_color_error" id="0x7f050048" />
    <public type="color" name="design_dark_default_color_on_background" id="0x7f050049" />
    <public type="color" name="design_dark_default_color_on_error" id="0x7f05004a" />
    <public type="color" name="design_dark_default_color_on_primary" id="0x7f05004b" />
    <public type="color" name="design_dark_default_color_on_secondary" id="0x7f05004c" />
    <public type="color" name="design_dark_default_color_on_surface" id="0x7f05004d" />
    <public type="color" name="design_dark_default_color_primary" id="0x7f05004e" />
    <public type="color" name="design_dark_default_color_primary_dark" id="0x7f05004f" />
    <public type="color" name="design_dark_default_color_primary_variant" id="0x7f050050" />
    <public type="color" name="design_dark_default_color_secondary" id="0x7f050051" />
    <public type="color" name="design_dark_default_color_secondary_variant" id="0x7f050052" />
    <public type="color" name="design_dark_default_color_surface" id="0x7f050053" />
    <public type="color" name="design_default_color_background" id="0x7f050054" />
    <public type="color" name="design_default_color_error" id="0x7f050055" />
    <public type="color" name="design_default_color_on_background" id="0x7f050056" />
    <public type="color" name="design_default_color_on_error" id="0x7f050057" />
    <public type="color" name="design_default_color_on_primary" id="0x7f050058" />
    <public type="color" name="design_default_color_on_secondary" id="0x7f050059" />
    <public type="color" name="design_default_color_on_surface" id="0x7f05005a" />
    <public type="color" name="design_default_color_primary" id="0x7f05005b" />
    <public type="color" name="design_default_color_primary_dark" id="0x7f05005c" />
    <public type="color" name="design_default_color_primary_variant" id="0x7f05005d" />
    <public type="color" name="design_default_color_secondary" id="0x7f05005e" />
    <public type="color" name="design_default_color_secondary_variant" id="0x7f05005f" />
    <public type="color" name="design_default_color_surface" id="0x7f050060" />
    <public type="color" name="design_error" id="0x7f050061" />
    <public type="color" name="design_icon_tint" id="0x7f050069" />
    <public type="color" name="design_snackbar_background_color" id="0x7f05006a" />
    <public type="color" name="error_color_material_dark" id="0x7f05006f" />
    <public type="color" name="error_color_material_light" id="0x7f050070" />
    <public type="color" name="foreground_material_dark" id="0x7f050071" />
    <public type="color" name="foreground_material_light" id="0x7f050072" />
    <public type="color" name="highlighted_text_material_dark" id="0x7f050073" />
    <public type="color" name="highlighted_text_material_light" id="0x7f050074" />
    <public type="color" name="link_blue" id="0x7f050076" />
    <public type="color" name="material_deep_teal_200" id="0x7f05022e" />
    <public type="color" name="material_deep_teal_500" id="0x7f05022f" />
    <public type="color" name="material_grey_100" id="0x7f05027a" />
    <public type="color" name="material_grey_50" id="0x7f05027c" />
    <public type="color" name="material_grey_600" id="0x7f05027d" />
    <public type="color" name="material_grey_800" id="0x7f05027e" />
    <public type="color" name="material_grey_850" id="0x7f05027f" />
    <public type="color" name="material_grey_900" id="0x7f050280" />
    <public type="color" name="material_on_surface_disabled" id="0x7f05028b" />
    <public type="color" name="material_on_surface_emphasis_high_type" id="0x7f05028c" />
    <public type="color" name="material_on_surface_emphasis_medium" id="0x7f05028d" />
    <public type="color" name="material_slider_active_tick_marks_color" id="0x7f0502c3" />
    <public type="color" name="material_slider_active_track_color" id="0x7f0502c4" />
    <public type="color" name="material_slider_halo_color" id="0x7f0502c5" />
    <public type="color" name="material_slider_inactive_tick_marks_color" id="0x7f0502c6" />
    <public type="color" name="material_slider_inactive_track_color" id="0x7f0502c7" />
    <public type="color" name="material_slider_thumb_color" id="0x7f0502c8" />
    <public type="color" name="material_timepicker_button_background" id="0x7f0502c9" />
    <public type="color" name="material_timepicker_button_stroke" id="0x7f0502ca" />
    <public type="color" name="material_timepicker_clock_text_color" id="0x7f0502cb" />
    <public type="color" name="material_timepicker_clockface" id="0x7f0502cc" />
    <public type="color" name="material_timepicker_modebutton_tint" id="0x7f0502cd" />
    <public type="color" name="mtrl_btn_bg_color_selector" id="0x7f0502ce" />
    <public type="color" name="mtrl_btn_ripple_color" id="0x7f0502cf" />
    <public type="color" name="mtrl_btn_stroke_color_selector" id="0x7f0502d0" />
    <public type="color" name="mtrl_btn_text_btn_bg_color_selector" id="0x7f0502d1" />
    <public type="color" name="mtrl_btn_text_btn_ripple_color" id="0x7f0502d2" />
    <public type="color" name="mtrl_btn_text_color_selector" id="0x7f0502d4" />
    <public type="color" name="mtrl_calendar_item_stroke_color" id="0x7f0502d6" />
    <public type="color" name="mtrl_calendar_selected_range" id="0x7f0502d7" />
    <public type="color" name="mtrl_card_view_foreground" id="0x7f0502d8" />
    <public type="color" name="mtrl_card_view_ripple" id="0x7f0502d9" />
    <public type="color" name="mtrl_chip_background_color" id="0x7f0502da" />
    <public type="color" name="mtrl_chip_close_icon_tint" id="0x7f0502db" />
    <public type="color" name="mtrl_chip_surface_color" id="0x7f0502dc" />
    <public type="color" name="mtrl_chip_text_color" id="0x7f0502dd" />
    <public type="color" name="mtrl_choice_chip_background_color" id="0x7f0502de" />
    <public type="color" name="mtrl_choice_chip_ripple_color" id="0x7f0502df" />
    <public type="color" name="mtrl_choice_chip_text_color" id="0x7f0502e0" />
    <public type="color" name="mtrl_error" id="0x7f0502e1" />
    <public type="color" name="mtrl_fab_bg_color_selector" id="0x7f0502e2" />
    <public type="color" name="mtrl_fab_icon_text_color_selector" id="0x7f0502e3" />
    <public type="color" name="mtrl_fab_ripple_color" id="0x7f0502e4" />
    <public type="color" name="mtrl_filled_background_color" id="0x7f0502e5" />
    <public type="color" name="mtrl_filled_icon_tint" id="0x7f0502e6" />
    <public type="color" name="mtrl_filled_stroke_color" id="0x7f0502e7" />
    <public type="color" name="mtrl_indicator_text_color" id="0x7f0502e8" />
    <public type="color" name="mtrl_navigation_bar_item_tint" id="0x7f0502eb" />
    <public type="color" name="mtrl_navigation_bar_ripple_color" id="0x7f0502ec" />
    <public type="color" name="mtrl_navigation_item_background_color" id="0x7f0502ed" />
    <public type="color" name="mtrl_navigation_item_icon_tint" id="0x7f0502ee" />
    <public type="color" name="mtrl_navigation_item_text_color" id="0x7f0502ef" />
    <public type="color" name="mtrl_on_primary_text_btn_text_color_selector" id="0x7f0502f0" />
    <public type="color" name="mtrl_on_surface_ripple_color" id="0x7f0502f1" />
    <public type="color" name="mtrl_outlined_icon_tint" id="0x7f0502f2" />
    <public type="color" name="mtrl_outlined_stroke_color" id="0x7f0502f3" />
    <public type="color" name="mtrl_popupmenu_overlay_color" id="0x7f0502f4" />
    <public type="color" name="mtrl_scrim_color" id="0x7f0502f5" />
    <public type="color" name="mtrl_tabs_icon_color_selector" id="0x7f0502fb" />
    <public type="color" name="mtrl_tabs_legacy_text_color_selector" id="0x7f0502fd" />
    <public type="color" name="mtrl_tabs_ripple_color" id="0x7f0502fe" />
    <public type="color" name="mtrl_text_btn_text_color_selector" id="0x7f0502ff" />
    <public type="color" name="mtrl_textinput_default_box_stroke_color" id="0x7f050300" />
    <public type="color" name="mtrl_textinput_disabled_color" id="0x7f050301" />
    <public type="color" name="mtrl_textinput_focused_box_stroke_color" id="0x7f050303" />
    <public type="color" name="mtrl_textinput_hovered_box_stroke_color" id="0x7f050304" />
    <public type="color" name="notification_action_color_filter" id="0x7f050307" />
    <public type="color" name="notification_icon_bg_color" id="0x7f050308" />
    <public type="color" name="primary_dark_material_dark" id="0x7f050309" />
    <public type="color" name="primary_dark_material_light" id="0x7f05030a" />
    <public type="color" name="primary_material_dark" id="0x7f05030b" />
    <public type="color" name="primary_material_light" id="0x7f05030c" />
    <public type="color" name="primary_text_default_material_dark" id="0x7f05030d" />
    <public type="color" name="primary_text_default_material_light" id="0x7f05030e" />
    <public type="color" name="primary_text_disabled_material_dark" id="0x7f05030f" />
    <public type="color" name="primary_text_disabled_material_light" id="0x7f050310" />
    <public type="color" name="ripple_material_dark" id="0x7f050311" />
    <public type="color" name="ripple_material_light" id="0x7f050312" />
    <public type="color" name="secondary_text_default_material_dark" id="0x7f050313" />
    <public type="color" name="secondary_text_default_material_light" id="0x7f050314" />
    <public type="color" name="secondary_text_disabled_material_dark" id="0x7f050315" />
    <public type="color" name="secondary_text_disabled_material_light" id="0x7f050316" />
    <public type="color" name="splash_bg_color" id="0x7f050317" />
    <public type="color" name="statusBarColor" id="0x7f050318" />
    <public type="color" name="switch_thumb_disabled_material_dark" id="0x7f050319" />
    <public type="color" name="switch_thumb_disabled_material_light" id="0x7f05031a" />
    <public type="color" name="switch_thumb_material_dark" id="0x7f05031b" />
    <public type="color" name="switch_thumb_material_light" id="0x7f05031c" />
    <public type="color" name="switch_thumb_normal_material_dark" id="0x7f05031d" />
    <public type="color" name="switch_thumb_normal_material_light" id="0x7f05031e" />
    <public type="color" name="tooltip_background_dark" id="0x7f05031f" />
    <public type="color" name="tooltip_background_light" id="0x7f050320" />
    <public type="color" name="vector_tint_color" id="0x7f050321" />
    <public type="color" name="vector_tint_theme_color" id="0x7f050322" />
    <public type="dimen" name="abc_action_bar_content_inset_material" id="0x7f060000" />
    <public type="dimen" name="abc_action_bar_content_inset_with_nav" id="0x7f060001" />
    <public type="dimen" name="abc_action_bar_default_height_material" id="0x7f060002" />
    <public type="dimen" name="abc_action_bar_default_padding_end_material" id="0x7f060003" />
    <public type="dimen" name="abc_action_bar_default_padding_start_material" id="0x7f060004" />
    <public type="dimen" name="abc_action_bar_elevation_material" id="0x7f060005" />
    <public type="dimen" name="abc_action_bar_icon_vertical_padding_material" id="0x7f060006" />
    <public type="dimen" name="abc_action_bar_overflow_padding_end_material" id="0x7f060007" />
    <public type="dimen" name="abc_action_bar_overflow_padding_start_material" id="0x7f060008" />
    <public type="dimen" name="abc_action_bar_stacked_max_height" id="0x7f060009" />
    <public type="dimen" name="abc_action_bar_stacked_tab_max_width" id="0x7f06000a" />
    <public type="dimen" name="abc_action_bar_subtitle_bottom_margin_material" id="0x7f06000b" />
    <public type="dimen" name="abc_action_bar_subtitle_top_margin_material" id="0x7f06000c" />
    <public type="dimen" name="abc_action_button_min_height_material" id="0x7f06000d" />
    <public type="dimen" name="abc_action_button_min_width_material" id="0x7f06000e" />
    <public type="dimen" name="abc_action_button_min_width_overflow_material" id="0x7f06000f" />
    <public type="dimen" name="abc_alert_dialog_button_bar_height" id="0x7f060010" />
    <public type="dimen" name="abc_alert_dialog_button_dimen" id="0x7f060011" />
    <public type="dimen" name="abc_button_inset_horizontal_material" id="0x7f060012" />
    <public type="dimen" name="abc_button_inset_vertical_material" id="0x7f060013" />
    <public type="dimen" name="abc_button_padding_horizontal_material" id="0x7f060014" />
    <public type="dimen" name="abc_button_padding_vertical_material" id="0x7f060015" />
    <public type="dimen" name="abc_cascading_menus_min_smallest_width" id="0x7f060016" />
    <public type="dimen" name="abc_config_prefDialogWidth" id="0x7f060017" />
    <public type="dimen" name="abc_control_corner_material" id="0x7f060018" />
    <public type="dimen" name="abc_control_inset_material" id="0x7f060019" />
    <public type="dimen" name="abc_control_padding_material" id="0x7f06001a" />
    <public type="dimen" name="abc_dialog_corner_radius_material" id="0x7f06001b" />
    <public type="dimen" name="abc_dialog_fixed_height_major" id="0x7f06001c" />
    <public type="dimen" name="abc_dialog_fixed_height_minor" id="0x7f06001d" />
    <public type="dimen" name="abc_dialog_fixed_width_major" id="0x7f06001e" />
    <public type="dimen" name="abc_dialog_fixed_width_minor" id="0x7f06001f" />
    <public type="dimen" name="abc_dialog_list_padding_bottom_no_buttons" id="0x7f060020" />
    <public type="dimen" name="abc_dialog_list_padding_top_no_title" id="0x7f060021" />
    <public type="dimen" name="abc_dialog_min_width_major" id="0x7f060022" />
    <public type="dimen" name="abc_dialog_min_width_minor" id="0x7f060023" />
    <public type="dimen" name="abc_dialog_padding_material" id="0x7f060024" />
    <public type="dimen" name="abc_dialog_padding_top_material" id="0x7f060025" />
    <public type="dimen" name="abc_dialog_title_divider_material" id="0x7f060026" />
    <public type="dimen" name="abc_disabled_alpha_material_dark" id="0x7f060027" />
    <public type="dimen" name="abc_disabled_alpha_material_light" id="0x7f060028" />
    <public type="dimen" name="abc_dropdownitem_icon_width" id="0x7f060029" />
    <public type="dimen" name="abc_dropdownitem_text_padding_left" id="0x7f06002a" />
    <public type="dimen" name="abc_dropdownitem_text_padding_right" id="0x7f06002b" />
    <public type="dimen" name="abc_edit_text_inset_bottom_material" id="0x7f06002c" />
    <public type="dimen" name="abc_edit_text_inset_horizontal_material" id="0x7f06002d" />
    <public type="dimen" name="abc_edit_text_inset_top_material" id="0x7f06002e" />
    <public type="dimen" name="abc_floating_window_z" id="0x7f06002f" />
    <public type="dimen" name="abc_list_item_height_large_material" id="0x7f060030" />
    <public type="dimen" name="abc_list_item_height_material" id="0x7f060031" />
    <public type="dimen" name="abc_list_item_height_small_material" id="0x7f060032" />
    <public type="dimen" name="abc_list_item_padding_horizontal_material" id="0x7f060033" />
    <public type="dimen" name="abc_panel_menu_list_width" id="0x7f060034" />
    <public type="dimen" name="abc_progress_bar_height_material" id="0x7f060035" />
    <public type="dimen" name="abc_search_view_preferred_height" id="0x7f060036" />
    <public type="dimen" name="abc_search_view_preferred_width" id="0x7f060037" />
    <public type="dimen" name="abc_seekbar_track_background_height_material" id="0x7f060038" />
    <public type="dimen" name="abc_seekbar_track_progress_height_material" id="0x7f060039" />
    <public type="dimen" name="abc_select_dialog_padding_start_material" id="0x7f06003a" />
    <public type="dimen" name="abc_star_big" id="0x7f06003b" />
    <public type="dimen" name="abc_star_medium" id="0x7f06003c" />
    <public type="dimen" name="abc_star_small" id="0x7f06003d" />
    <public type="dimen" name="abc_switch_padding" id="0x7f06003e" />
    <public type="dimen" name="abc_text_size_body_1_material" id="0x7f06003f" />
    <public type="dimen" name="abc_text_size_body_2_material" id="0x7f060040" />
    <public type="dimen" name="abc_text_size_button_material" id="0x7f060041" />
    <public type="dimen" name="abc_text_size_caption_material" id="0x7f060042" />
    <public type="dimen" name="abc_text_size_display_1_material" id="0x7f060043" />
    <public type="dimen" name="abc_text_size_display_2_material" id="0x7f060044" />
    <public type="dimen" name="abc_text_size_display_3_material" id="0x7f060045" />
    <public type="dimen" name="abc_text_size_display_4_material" id="0x7f060046" />
    <public type="dimen" name="abc_text_size_headline_material" id="0x7f060047" />
    <public type="dimen" name="abc_text_size_large_material" id="0x7f060048" />
    <public type="dimen" name="abc_text_size_medium_material" id="0x7f060049" />
    <public type="dimen" name="abc_text_size_menu_header_material" id="0x7f06004a" />
    <public type="dimen" name="abc_text_size_menu_material" id="0x7f06004b" />
    <public type="dimen" name="abc_text_size_small_material" id="0x7f06004c" />
    <public type="dimen" name="abc_text_size_subhead_material" id="0x7f06004d" />
    <public type="dimen" name="abc_text_size_subtitle_material_toolbar" id="0x7f06004e" />
    <public type="dimen" name="abc_text_size_title_material" id="0x7f06004f" />
    <public type="dimen" name="abc_text_size_title_material_toolbar" id="0x7f060050" />
    <public type="dimen" name="appcompat_dialog_background_inset" id="0x7f060051" />
    <public type="dimen" name="browser_actions_context_menu_max_width" id="0x7f060052" />
    <public type="dimen" name="browser_actions_context_menu_min_padding" id="0x7f060053" />
    <public type="dimen" name="cardview_compat_inset_shadow" id="0x7f060054" />
    <public type="dimen" name="cardview_default_elevation" id="0x7f060055" />
    <public type="dimen" name="cardview_default_radius" id="0x7f060056" />
    <public type="dimen" name="clock_face_margin_start" id="0x7f060057" />
    <public type="dimen" name="compat_button_inset_horizontal_material" id="0x7f060058" />
    <public type="dimen" name="compat_button_inset_vertical_material" id="0x7f060059" />
    <public type="dimen" name="compat_button_padding_horizontal_material" id="0x7f06005a" />
    <public type="dimen" name="compat_button_padding_vertical_material" id="0x7f06005b" />
    <public type="dimen" name="compat_control_corner_material" id="0x7f06005c" />
    <public type="dimen" name="compat_notification_large_icon_max_height" id="0x7f06005d" />
    <public type="dimen" name="compat_notification_large_icon_max_width" id="0x7f06005e" />
    <public type="dimen" name="design_appbar_elevation" id="0x7f060060" />
    <public type="dimen" name="design_bottom_navigation_active_item_max_width" id="0x7f060061" />
    <public type="dimen" name="design_bottom_navigation_active_item_min_width" id="0x7f060062" />
    <public type="dimen" name="design_bottom_navigation_active_text_size" id="0x7f060063" />
    <public type="dimen" name="design_bottom_navigation_elevation" id="0x7f060064" />
    <public type="dimen" name="design_bottom_navigation_height" id="0x7f060065" />
    <public type="dimen" name="design_bottom_navigation_icon_size" id="0x7f060066" />
    <public type="dimen" name="design_bottom_navigation_item_max_width" id="0x7f060067" />
    <public type="dimen" name="design_bottom_navigation_item_min_width" id="0x7f060068" />
    <public type="dimen" name="design_bottom_navigation_label_padding" id="0x7f060069" />
    <public type="dimen" name="design_bottom_navigation_margin" id="0x7f06006a" />
    <public type="dimen" name="design_bottom_navigation_text_size" id="0x7f06006c" />
    <public type="dimen" name="design_bottom_sheet_elevation" id="0x7f06006d" />
    <public type="dimen" name="design_bottom_sheet_modal_elevation" id="0x7f06006e" />
    <public type="dimen" name="design_bottom_sheet_peek_height_min" id="0x7f06006f" />
    <public type="dimen" name="design_fab_border_width" id="0x7f060070" />
    <public type="dimen" name="design_fab_elevation" id="0x7f060071" />
    <public type="dimen" name="design_fab_image_size" id="0x7f060072" />
    <public type="dimen" name="design_fab_size_normal" id="0x7f060074" />
    <public type="dimen" name="design_fab_translation_z_hovered_focused" id="0x7f060075" />
    <public type="dimen" name="design_fab_translation_z_pressed" id="0x7f060076" />
    <public type="dimen" name="design_navigation_elevation" id="0x7f060077" />
    <public type="dimen" name="design_navigation_icon_padding" id="0x7f060078" />
    <public type="dimen" name="design_navigation_icon_size" id="0x7f060079" />
    <public type="dimen" name="design_navigation_item_horizontal_padding" id="0x7f06007a" />
    <public type="dimen" name="design_navigation_item_icon_padding" id="0x7f06007b" />
    <public type="dimen" name="design_navigation_max_width" id="0x7f06007d" />
    <public type="dimen" name="design_snackbar_action_inline_max_width" id="0x7f060080" />
    <public type="dimen" name="design_snackbar_action_text_color_alpha" id="0x7f060081" />
    <public type="dimen" name="design_snackbar_background_corner_radius" id="0x7f060082" />
    <public type="dimen" name="design_snackbar_elevation" id="0x7f060083" />
    <public type="dimen" name="design_snackbar_max_width" id="0x7f060085" />
    <public type="dimen" name="design_snackbar_min_width" id="0x7f060086" />
    <public type="dimen" name="design_snackbar_padding_horizontal" id="0x7f060087" />
    <public type="dimen" name="design_snackbar_padding_vertical" id="0x7f060088" />
    <public type="dimen" name="design_snackbar_padding_vertical_2lines" id="0x7f060089" />
    <public type="dimen" name="design_snackbar_text_size" id="0x7f06008a" />
    <public type="dimen" name="design_tab_max_width" id="0x7f06008b" />
    <public type="dimen" name="design_tab_text_size" id="0x7f06008d" />
    <public type="dimen" name="design_textinput_caption_translate_y" id="0x7f06008f" />
    <public type="dimen" name="disabled_alpha_material_dark" id="0x7f060090" />
    <public type="dimen" name="disabled_alpha_material_light" id="0x7f060091" />
    <public type="dimen" name="fastscroll_default_thickness" id="0x7f060092" />
    <public type="dimen" name="fastscroll_margin" id="0x7f060093" />
    <public type="dimen" name="fastscroll_minimum_range" id="0x7f060094" />
    <public type="dimen" name="highlight_alpha_material_colored" id="0x7f060095" />
    <public type="dimen" name="highlight_alpha_material_dark" id="0x7f060096" />
    <public type="dimen" name="highlight_alpha_material_light" id="0x7f060097" />
    <public type="dimen" name="hint_alpha_material_dark" id="0x7f060098" />
    <public type="dimen" name="hint_alpha_material_light" id="0x7f060099" />
    <public type="dimen" name="hint_pressed_alpha_material_dark" id="0x7f06009a" />
    <public type="dimen" name="hint_pressed_alpha_material_light" id="0x7f06009b" />
    <public type="dimen" name="item_touch_helper_max_drag_scroll_per_frame" id="0x7f06009c" />
    <public type="dimen" name="item_touch_helper_swipe_escape_max_velocity" id="0x7f06009d" />
    <public type="dimen" name="item_touch_helper_swipe_escape_velocity" id="0x7f06009e" />
    <public type="dimen" name="m3_back_progress_bottom_container_max_scale_x_distance" id="0x7f0600ae" />
    <public type="dimen" name="m3_back_progress_bottom_container_max_scale_y_distance" id="0x7f0600af" />
    <public type="dimen" name="m3_back_progress_side_container_max_scale_x_distance_grow" id="0x7f0600b2" />
    <public type="dimen" name="m3_back_progress_side_container_max_scale_x_distance_shrink" id="0x7f0600b3" />
    <public type="dimen" name="m3_back_progress_side_container_max_scale_y_distance" id="0x7f0600b4" />
    <public type="dimen" name="m3_badge_size" id="0x7f0600b7" />
    <public type="dimen" name="m3_badge_with_text_size" id="0x7f0600bb" />
    <public type="dimen" name="m3_badge_with_text_vertical_padding" id="0x7f0600bd" />
    <public type="dimen" name="m3_carousel_debug_keyline_width" id="0x7f0600ef" />
    <public type="dimen" name="m3_carousel_small_item_size_max" id="0x7f0600f3" />
    <public type="dimen" name="m3_carousel_small_item_size_min" id="0x7f0600f4" />
    <public type="dimen" name="m3_comp_badge_large_size" id="0x7f060101" />
    <public type="dimen" name="m3_comp_badge_size" id="0x7f060102" />
    <public type="dimen" name="m3_comp_outlined_autocomplete_menu_container_elevation" id="0x7f06014f" />
    <public type="dimen" name="m3_comp_outlined_text_field_focus_outline_width" id="0x7f06015a" />
    <public type="dimen" name="m3_comp_outlined_text_field_outline_width" id="0x7f06015b" />
    <public type="dimen" name="m3_comp_scrim_container_opacity" id="0x7f06016f" />
    <public type="dimen" name="m3_comp_sheet_side_docked_standard_container_elevation" id="0x7f060182" />
    <public type="dimen" name="m3_navigation_item_active_indicator_label_padding" id="0x7f0601c1" />
    <public type="dimen" name="m3_searchbar_text_size" id="0x7f0601e3" />
    <public type="dimen" name="m3_side_sheet_standard_elevation" id="0x7f0601e9" />
    <public type="dimen" name="m3_sys_elevation_level0" id="0x7f0601f2" />
    <public type="dimen" name="m3_sys_elevation_level2" id="0x7f0601f4" />
    <public type="dimen" name="material_bottom_sheet_max_width" id="0x7f060223" />
    <public type="dimen" name="material_clock_display_height" id="0x7f060224" />
    <public type="dimen" name="material_clock_display_padding" id="0x7f060225" />
    <public type="dimen" name="material_clock_display_width" id="0x7f060226" />
    <public type="dimen" name="material_clock_hand_center_dot_radius" id="0x7f060229" />
    <public type="dimen" name="material_clock_hand_padding" id="0x7f06022a" />
    <public type="dimen" name="material_clock_hand_stroke_width" id="0x7f06022b" />
    <public type="dimen" name="material_clock_number_text_size" id="0x7f06022c" />
    <public type="dimen" name="material_clock_period_toggle_height" id="0x7f06022d" />
    <public type="dimen" name="material_clock_period_toggle_horizontal_gap" id="0x7f06022e" />
    <public type="dimen" name="material_clock_period_toggle_vertical_gap" id="0x7f06022f" />
    <public type="dimen" name="material_clock_period_toggle_width" id="0x7f060230" />
    <public type="dimen" name="material_clock_size" id="0x7f060231" />
    <public type="dimen" name="material_cursor_inset" id="0x7f060232" />
    <public type="dimen" name="material_cursor_width" id="0x7f060233" />
    <public type="dimen" name="material_emphasis_disabled" id="0x7f060235" />
    <public type="dimen" name="material_emphasis_high_type" id="0x7f060237" />
    <public type="dimen" name="material_emphasis_medium" id="0x7f060238" />
    <public type="dimen" name="material_filled_edittext_font_1_3_padding_bottom" id="0x7f060239" />
    <public type="dimen" name="material_filled_edittext_font_1_3_padding_top" id="0x7f06023a" />
    <public type="dimen" name="material_filled_edittext_font_2_0_padding_bottom" id="0x7f06023b" />
    <public type="dimen" name="material_filled_edittext_font_2_0_padding_top" id="0x7f06023c" />
    <public type="dimen" name="material_font_1_3_box_collapsed_padding_top" id="0x7f06023d" />
    <public type="dimen" name="material_font_2_0_box_collapsed_padding_top" id="0x7f06023e" />
    <public type="dimen" name="material_helper_text_default_padding_top" id="0x7f06023f" />
    <public type="dimen" name="material_helper_text_font_1_3_padding_horizontal" id="0x7f060240" />
    <public type="dimen" name="material_helper_text_font_1_3_padding_top" id="0x7f060241" />
    <public type="dimen" name="material_input_text_to_prefix_suffix_padding" id="0x7f060242" />
    <public type="dimen" name="material_textinput_max_width" id="0x7f060244" />
    <public type="dimen" name="material_textinput_min_width" id="0x7f060245" />
    <public type="dimen" name="material_time_picker_minimum_screen_height" id="0x7f060246" />
    <public type="dimen" name="material_time_picker_minimum_screen_width" id="0x7f060247" />
    <public type="dimen" name="mtrl_alert_dialog_background_inset_bottom" id="0x7f060248" />
    <public type="dimen" name="mtrl_alert_dialog_background_inset_end" id="0x7f060249" />
    <public type="dimen" name="mtrl_alert_dialog_background_inset_start" id="0x7f06024a" />
    <public type="dimen" name="mtrl_alert_dialog_background_inset_top" id="0x7f06024b" />
    <public type="dimen" name="mtrl_badge_horizontal_edge_offset" id="0x7f06024d" />
    <public type="dimen" name="mtrl_badge_long_text_horizontal_padding" id="0x7f06024e" />
    <public type="dimen" name="mtrl_badge_size" id="0x7f06024f" />
    <public type="dimen" name="mtrl_badge_text_horizontal_edge_offset" id="0x7f060250" />
    <public type="dimen" name="mtrl_badge_text_size" id="0x7f060251" />
    <public type="dimen" name="mtrl_badge_with_text_size" id="0x7f060254" />
    <public type="dimen" name="mtrl_bottomappbar_fab_cradle_margin" id="0x7f060257" />
    <public type="dimen" name="mtrl_bottomappbar_fab_cradle_rounded_corner_radius" id="0x7f060258" />
    <public type="dimen" name="mtrl_bottomappbar_fab_cradle_vertical_offset" id="0x7f060259" />
    <public type="dimen" name="mtrl_bottomappbar_height" id="0x7f06025a" />
    <public type="dimen" name="mtrl_btn_dialog_btn_min_width" id="0x7f06025c" />
    <public type="dimen" name="mtrl_btn_disabled_elevation" id="0x7f06025d" />
    <public type="dimen" name="mtrl_btn_disabled_z" id="0x7f06025e" />
    <public type="dimen" name="mtrl_btn_elevation" id="0x7f06025f" />
    <public type="dimen" name="mtrl_btn_focused_z" id="0x7f060260" />
    <public type="dimen" name="mtrl_btn_hovered_z" id="0x7f060261" />
    <public type="dimen" name="mtrl_btn_icon_padding" id="0x7f060263" />
    <public type="dimen" name="mtrl_btn_inset" id="0x7f060264" />
    <public type="dimen" name="mtrl_btn_max_width" id="0x7f060266" />
    <public type="dimen" name="mtrl_btn_padding_bottom" id="0x7f060267" />
    <public type="dimen" name="mtrl_btn_padding_left" id="0x7f060268" />
    <public type="dimen" name="mtrl_btn_padding_right" id="0x7f060269" />
    <public type="dimen" name="mtrl_btn_padding_top" id="0x7f06026a" />
    <public type="dimen" name="mtrl_btn_pressed_z" id="0x7f06026b" />
    <public type="dimen" name="mtrl_btn_stroke_size" id="0x7f06026d" />
    <public type="dimen" name="mtrl_btn_text_btn_icon_padding" id="0x7f06026e" />
    <public type="dimen" name="mtrl_btn_text_btn_padding_left" id="0x7f06026f" />
    <public type="dimen" name="mtrl_btn_text_btn_padding_right" id="0x7f060270" />
    <public type="dimen" name="mtrl_btn_z" id="0x7f060272" />
    <public type="dimen" name="mtrl_calendar_action_confirm_button_min_width" id="0x7f060273" />
    <public type="dimen" name="mtrl_calendar_action_height" id="0x7f060274" />
    <public type="dimen" name="mtrl_calendar_action_padding" id="0x7f060275" />
    <public type="dimen" name="mtrl_calendar_bottom_padding" id="0x7f060276" />
    <public type="dimen" name="mtrl_calendar_content_padding" id="0x7f060277" />
    <public type="dimen" name="mtrl_calendar_day_corner" id="0x7f060278" />
    <public type="dimen" name="mtrl_calendar_day_height" id="0x7f060279" />
    <public type="dimen" name="mtrl_calendar_day_horizontal_padding" id="0x7f06027a" />
    <public type="dimen" name="mtrl_calendar_day_today_stroke" id="0x7f06027b" />
    <public type="dimen" name="mtrl_calendar_day_vertical_padding" id="0x7f06027c" />
    <public type="dimen" name="mtrl_calendar_day_width" id="0x7f06027d" />
    <public type="dimen" name="mtrl_calendar_days_of_week_height" id="0x7f06027e" />
    <public type="dimen" name="mtrl_calendar_dialog_background_inset" id="0x7f06027f" />
    <public type="dimen" name="mtrl_calendar_header_content_padding" id="0x7f060280" />
    <public type="dimen" name="mtrl_calendar_header_content_padding_fullscreen" id="0x7f060281" />
    <public type="dimen" name="mtrl_calendar_header_divider_thickness" id="0x7f060282" />
    <public type="dimen" name="mtrl_calendar_header_height" id="0x7f060283" />
    <public type="dimen" name="mtrl_calendar_header_height_fullscreen" id="0x7f060284" />
    <public type="dimen" name="mtrl_calendar_header_selection_line_height" id="0x7f060285" />
    <public type="dimen" name="mtrl_calendar_header_text_padding" id="0x7f060286" />
    <public type="dimen" name="mtrl_calendar_header_toggle_margin_bottom" id="0x7f060287" />
    <public type="dimen" name="mtrl_calendar_header_toggle_margin_top" id="0x7f060288" />
    <public type="dimen" name="mtrl_calendar_landscape_header_width" id="0x7f060289" />
    <public type="dimen" name="mtrl_calendar_month_horizontal_padding" id="0x7f06028b" />
    <public type="dimen" name="mtrl_calendar_month_vertical_padding" id="0x7f06028c" />
    <public type="dimen" name="mtrl_calendar_navigation_bottom_padding" id="0x7f06028d" />
    <public type="dimen" name="mtrl_calendar_navigation_height" id="0x7f06028e" />
    <public type="dimen" name="mtrl_calendar_navigation_top_padding" id="0x7f06028f" />
    <public type="dimen" name="mtrl_calendar_pre_l_text_clip_padding" id="0x7f060290" />
    <public type="dimen" name="mtrl_calendar_selection_baseline_to_top_fullscreen" id="0x7f060291" />
    <public type="dimen" name="mtrl_calendar_selection_text_baseline_to_top" id="0x7f060294" />
    <public type="dimen" name="mtrl_calendar_title_baseline_to_top" id="0x7f060296" />
    <public type="dimen" name="mtrl_calendar_title_baseline_to_top_fullscreen" id="0x7f060297" />
    <public type="dimen" name="mtrl_calendar_year_corner" id="0x7f060298" />
    <public type="dimen" name="mtrl_calendar_year_height" id="0x7f060299" />
    <public type="dimen" name="mtrl_calendar_year_horizontal_padding" id="0x7f06029a" />
    <public type="dimen" name="mtrl_calendar_year_vertical_padding" id="0x7f06029b" />
    <public type="dimen" name="mtrl_calendar_year_width" id="0x7f06029c" />
    <public type="dimen" name="mtrl_card_checked_icon_margin" id="0x7f06029d" />
    <public type="dimen" name="mtrl_card_checked_icon_size" id="0x7f06029e" />
    <public type="dimen" name="mtrl_card_dragged_z" id="0x7f0602a0" />
    <public type="dimen" name="mtrl_card_elevation" id="0x7f0602a1" />
    <public type="dimen" name="mtrl_chip_pressed_translation_z" id="0x7f0602a3" />
    <public type="dimen" name="mtrl_chip_text_size" id="0x7f0602a4" />
    <public type="dimen" name="mtrl_exposed_dropdown_menu_popup_elevation" id="0x7f0602a5" />
    <public type="dimen" name="mtrl_exposed_dropdown_menu_popup_vertical_offset" id="0x7f0602a6" />
    <public type="dimen" name="mtrl_exposed_dropdown_menu_popup_vertical_padding" id="0x7f0602a7" />
    <public type="dimen" name="mtrl_extended_fab_bottom_padding" id="0x7f0602a8" />
    <public type="dimen" name="mtrl_extended_fab_disabled_elevation" id="0x7f0602a9" />
    <public type="dimen" name="mtrl_extended_fab_disabled_translation_z" id="0x7f0602aa" />
    <public type="dimen" name="mtrl_extended_fab_elevation" id="0x7f0602ab" />
    <public type="dimen" name="mtrl_extended_fab_end_padding" id="0x7f0602ac" />
    <public type="dimen" name="mtrl_extended_fab_end_padding_icon" id="0x7f0602ad" />
    <public type="dimen" name="mtrl_extended_fab_icon_size" id="0x7f0602ae" />
    <public type="dimen" name="mtrl_extended_fab_icon_text_spacing" id="0x7f0602af" />
    <public type="dimen" name="mtrl_extended_fab_min_height" id="0x7f0602b0" />
    <public type="dimen" name="mtrl_extended_fab_min_width" id="0x7f0602b1" />
    <public type="dimen" name="mtrl_extended_fab_start_padding" id="0x7f0602b2" />
    <public type="dimen" name="mtrl_extended_fab_start_padding_icon" id="0x7f0602b3" />
    <public type="dimen" name="mtrl_extended_fab_top_padding" id="0x7f0602b4" />
    <public type="dimen" name="mtrl_extended_fab_translation_z_base" id="0x7f0602b5" />
    <public type="dimen" name="mtrl_extended_fab_translation_z_hovered_focused" id="0x7f0602b6" />
    <public type="dimen" name="mtrl_extended_fab_translation_z_pressed" id="0x7f0602b7" />
    <public type="dimen" name="mtrl_fab_elevation" id="0x7f0602b8" />
    <public type="dimen" name="mtrl_fab_translation_z_hovered_focused" id="0x7f0602ba" />
    <public type="dimen" name="mtrl_fab_translation_z_pressed" id="0x7f0602bb" />
    <public type="dimen" name="mtrl_high_ripple_default_alpha" id="0x7f0602bc" />
    <public type="dimen" name="mtrl_high_ripple_focused_alpha" id="0x7f0602bd" />
    <public type="dimen" name="mtrl_high_ripple_hovered_alpha" id="0x7f0602be" />
    <public type="dimen" name="mtrl_high_ripple_pressed_alpha" id="0x7f0602bf" />
    <public type="dimen" name="mtrl_low_ripple_default_alpha" id="0x7f0602c0" />
    <public type="dimen" name="mtrl_low_ripple_focused_alpha" id="0x7f0602c1" />
    <public type="dimen" name="mtrl_low_ripple_hovered_alpha" id="0x7f0602c2" />
    <public type="dimen" name="mtrl_low_ripple_pressed_alpha" id="0x7f0602c3" />
    <public type="dimen" name="mtrl_min_touch_target_size" id="0x7f0602c4" />
    <public type="dimen" name="mtrl_navigation_bar_item_default_icon_size" id="0x7f0602c5" />
    <public type="dimen" name="mtrl_navigation_bar_item_default_margin" id="0x7f0602c6" />
    <public type="dimen" name="mtrl_navigation_item_horizontal_padding" id="0x7f0602c8" />
    <public type="dimen" name="mtrl_navigation_item_icon_padding" id="0x7f0602c9" />
    <public type="dimen" name="mtrl_navigation_item_icon_size" id="0x7f0602ca" />
    <public type="dimen" name="mtrl_navigation_item_shape_horizontal_margin" id="0x7f0602cb" />
    <public type="dimen" name="mtrl_navigation_item_shape_vertical_margin" id="0x7f0602cc" />
    <public type="dimen" name="mtrl_navigation_rail_default_width" id="0x7f0602cf" />
    <public type="dimen" name="mtrl_navigation_rail_elevation" id="0x7f0602d0" />
    <public type="dimen" name="mtrl_navigation_rail_icon_margin" id="0x7f0602d1" />
    <public type="dimen" name="mtrl_navigation_rail_icon_size" id="0x7f0602d2" />
    <public type="dimen" name="mtrl_navigation_rail_text_bottom_margin" id="0x7f0602d4" />
    <public type="dimen" name="mtrl_progress_circular_inset_medium" id="0x7f0602d8" />
    <public type="dimen" name="mtrl_progress_circular_size_medium" id="0x7f0602dd" />
    <public type="dimen" name="mtrl_progress_circular_track_thickness_medium" id="0x7f0602e0" />
    <public type="dimen" name="mtrl_progress_track_thickness" id="0x7f0602e3" />
    <public type="dimen" name="mtrl_shape_corner_size_large_component" id="0x7f0602e4" />
    <public type="dimen" name="mtrl_shape_corner_size_medium_component" id="0x7f0602e5" />
    <public type="dimen" name="mtrl_shape_corner_size_small_component" id="0x7f0602e6" />
    <public type="dimen" name="mtrl_slider_halo_radius" id="0x7f0602e7" />
    <public type="dimen" name="mtrl_slider_thumb_elevation" id="0x7f0602eb" />
    <public type="dimen" name="mtrl_slider_thumb_radius" id="0x7f0602ec" />
    <public type="dimen" name="mtrl_slider_tick_radius" id="0x7f0602ee" />
    <public type="dimen" name="mtrl_slider_track_height" id="0x7f0602ef" />
    <public type="dimen" name="mtrl_snackbar_action_text_color_alpha" id="0x7f0602f2" />
    <public type="dimen" name="mtrl_snackbar_background_corner_radius" id="0x7f0602f3" />
    <public type="dimen" name="mtrl_snackbar_background_overlay_color_alpha" id="0x7f0602f4" />
    <public type="dimen" name="mtrl_snackbar_margin" id="0x7f0602f5" />
    <public type="dimen" name="mtrl_snackbar_message_margin_horizontal" id="0x7f0602f6" />
    <public type="dimen" name="mtrl_snackbar_padding_horizontal" id="0x7f0602f7" />
    <public type="dimen" name="mtrl_textinput_box_corner_radius_small" id="0x7f0602ff" />
    <public type="dimen" name="mtrl_textinput_box_label_cutout_padding" id="0x7f060300" />
    <public type="dimen" name="mtrl_textinput_box_stroke_width_default" id="0x7f060301" />
    <public type="dimen" name="mtrl_textinput_box_stroke_width_focused" id="0x7f060302" />
    <public type="dimen" name="mtrl_textinput_counter_margin_start" id="0x7f060303" />
    <public type="dimen" name="mtrl_textinput_end_icon_margin_start" id="0x7f060304" />
    <public type="dimen" name="mtrl_textinput_start_icon_margin_end" id="0x7f060306" />
    <public type="dimen" name="mtrl_toolbar_default_height" id="0x7f060307" />
    <public type="dimen" name="mtrl_tooltip_minHeight" id="0x7f06030a" />
    <public type="dimen" name="mtrl_tooltip_minWidth" id="0x7f06030b" />
    <public type="dimen" name="mtrl_tooltip_padding" id="0x7f06030c" />
    <public type="dimen" name="notification_action_icon_size" id="0x7f06030e" />
    <public type="dimen" name="notification_action_text_size" id="0x7f06030f" />
    <public type="dimen" name="notification_big_circle_margin" id="0x7f060310" />
    <public type="dimen" name="notification_content_margin_start" id="0x7f060311" />
    <public type="dimen" name="notification_large_icon_height" id="0x7f060312" />
    <public type="dimen" name="notification_large_icon_width" id="0x7f060313" />
    <public type="dimen" name="notification_main_column_padding_top" id="0x7f060314" />
    <public type="dimen" name="notification_media_narrow_margin" id="0x7f060315" />
    <public type="dimen" name="notification_right_icon_size" id="0x7f060316" />
    <public type="dimen" name="notification_right_side_padding_top" id="0x7f060317" />
    <public type="dimen" name="notification_small_icon_background_padding" id="0x7f060318" />
    <public type="dimen" name="notification_small_icon_size_as_large" id="0x7f060319" />
    <public type="dimen" name="notification_subtext_size" id="0x7f06031a" />
    <public type="dimen" name="notification_top_pad" id="0x7f06031b" />
    <public type="dimen" name="notification_top_pad_large_text" id="0x7f06031c" />
    <public type="dimen" name="splashscreen_icon_mask_size_no_background" id="0x7f06031d" />
    <public type="dimen" name="splashscreen_icon_mask_stroke_no_background" id="0x7f06031f" />
    <public type="dimen" name="splashscreen_icon_size_no_background" id="0x7f060322" />
    <public type="dimen" name="tooltip_corner_radius" id="0x7f060324" />
    <public type="dimen" name="tooltip_horizontal_padding" id="0x7f060325" />
    <public type="dimen" name="tooltip_margin" id="0x7f060326" />
    <public type="dimen" name="tooltip_precise_anchor_extra_offset" id="0x7f060327" />
    <public type="dimen" name="tooltip_precise_anchor_threshold" id="0x7f060328" />
    <public type="dimen" name="tooltip_vertical_padding" id="0x7f060329" />
    <public type="dimen" name="tooltip_y_offset_non_touch" id="0x7f06032a" />
    <public type="dimen" name="tooltip_y_offset_touch" id="0x7f06032b" />
    <public type="drawable" name="$avd_hide_password__0" id="0x7f070000" />
    <public type="drawable" name="$avd_hide_password__1" id="0x7f070001" />
    <public type="drawable" name="$avd_hide_password__2" id="0x7f070002" />
    <public type="drawable" name="$avd_show_password__0" id="0x7f070003" />
    <public type="drawable" name="$avd_show_password__1" id="0x7f070004" />
    <public type="drawable" name="$avd_show_password__2" id="0x7f070005" />
    <public type="drawable" name="$mtrl_checkbox_button_checked_unchecked__0" id="0x7f07000d" />
    <public type="drawable" name="$mtrl_checkbox_button_checked_unchecked__1" id="0x7f07000e" />
    <public type="drawable" name="$mtrl_checkbox_button_checked_unchecked__2" id="0x7f07000f" />
    <public type="drawable" name="$mtrl_checkbox_button_icon_checked_indeterminate__0" id="0x7f070010" />
    <public type="drawable" name="$mtrl_checkbox_button_icon_checked_unchecked__0" id="0x7f070011" />
    <public type="drawable" name="$mtrl_checkbox_button_icon_checked_unchecked__1" id="0x7f070012" />
    <public type="drawable" name="$mtrl_checkbox_button_icon_checked_unchecked__2" id="0x7f070013" />
    <public type="drawable" name="$mtrl_checkbox_button_icon_indeterminate_checked__0" id="0x7f070014" />
    <public type="drawable" name="$mtrl_checkbox_button_icon_indeterminate_unchecked__0" id="0x7f070015" />
    <public type="drawable" name="$mtrl_checkbox_button_icon_indeterminate_unchecked__1" id="0x7f070016" />
    <public type="drawable" name="$mtrl_checkbox_button_icon_indeterminate_unchecked__2" id="0x7f070017" />
    <public type="drawable" name="$mtrl_checkbox_button_icon_unchecked_checked__0" id="0x7f070018" />
    <public type="drawable" name="$mtrl_checkbox_button_icon_unchecked_checked__1" id="0x7f070019" />
    <public type="drawable" name="$mtrl_checkbox_button_icon_unchecked_checked__2" id="0x7f07001a" />
    <public type="drawable" name="$mtrl_checkbox_button_icon_unchecked_indeterminate__0" id="0x7f07001b" />
    <public type="drawable" name="$mtrl_checkbox_button_icon_unchecked_indeterminate__1" id="0x7f07001c" />
    <public type="drawable" name="$mtrl_checkbox_button_icon_unchecked_indeterminate__2" id="0x7f07001d" />
    <public type="drawable" name="$mtrl_checkbox_button_unchecked_checked__0" id="0x7f07001e" />
    <public type="drawable" name="$mtrl_checkbox_button_unchecked_checked__1" id="0x7f07001f" />
    <public type="drawable" name="$mtrl_checkbox_button_unchecked_checked__2" id="0x7f070020" />
    <public type="drawable" name="abc_ab_share_pack_mtrl_alpha" id="0x7f070029" />
    <public type="drawable" name="abc_action_bar_item_background_material" id="0x7f07002a" />
    <public type="drawable" name="abc_btn_borderless_material" id="0x7f07002b" />
    <public type="drawable" name="abc_btn_check_material" id="0x7f07002c" />
    <public type="drawable" name="abc_btn_check_material_anim" id="0x7f07002d" />
    <public type="drawable" name="abc_btn_check_to_on_mtrl_000" id="0x7f07002e" />
    <public type="drawable" name="abc_btn_check_to_on_mtrl_015" id="0x7f07002f" />
    <public type="drawable" name="abc_btn_colored_material" id="0x7f070030" />
    <public type="drawable" name="abc_btn_default_mtrl_shape" id="0x7f070031" />
    <public type="drawable" name="abc_btn_radio_material" id="0x7f070032" />
    <public type="drawable" name="abc_btn_radio_material_anim" id="0x7f070033" />
    <public type="drawable" name="abc_btn_radio_to_on_mtrl_000" id="0x7f070034" />
    <public type="drawable" name="abc_btn_radio_to_on_mtrl_015" id="0x7f070035" />
    <public type="drawable" name="abc_btn_switch_to_on_mtrl_00001" id="0x7f070036" />
    <public type="drawable" name="abc_btn_switch_to_on_mtrl_00012" id="0x7f070037" />
    <public type="drawable" name="abc_cab_background_internal_bg" id="0x7f070038" />
    <public type="drawable" name="abc_cab_background_top_material" id="0x7f070039" />
    <public type="drawable" name="abc_cab_background_top_mtrl_alpha" id="0x7f07003a" />
    <public type="drawable" name="abc_control_background_material" id="0x7f07003b" />
    <public type="drawable" name="abc_dialog_material_background" id="0x7f07003c" />
    <public type="drawable" name="abc_edit_text_material" id="0x7f07003d" />
    <public type="drawable" name="abc_ic_ab_back_material" id="0x7f07003e" />
    <public type="drawable" name="abc_ic_arrow_drop_right_black_24dp" id="0x7f07003f" />
    <public type="drawable" name="abc_ic_clear_material" id="0x7f070040" />
    <public type="drawable" name="abc_ic_commit_search_api_mtrl_alpha" id="0x7f070041" />
    <public type="drawable" name="abc_ic_go_search_api_material" id="0x7f070042" />
    <public type="drawable" name="abc_ic_menu_copy_mtrl_am_alpha" id="0x7f070043" />
    <public type="drawable" name="abc_ic_menu_cut_mtrl_alpha" id="0x7f070044" />
    <public type="drawable" name="abc_ic_menu_overflow_material" id="0x7f070045" />
    <public type="drawable" name="abc_ic_menu_paste_mtrl_am_alpha" id="0x7f070046" />
    <public type="drawable" name="abc_ic_menu_selectall_mtrl_alpha" id="0x7f070047" />
    <public type="drawable" name="abc_ic_menu_share_mtrl_alpha" id="0x7f070048" />
    <public type="drawable" name="abc_ic_search_api_material" id="0x7f070049" />
    <public type="drawable" name="abc_ic_voice_search_api_material" id="0x7f07004a" />
    <public type="drawable" name="abc_item_background_holo_dark" id="0x7f07004b" />
    <public type="drawable" name="abc_item_background_holo_light" id="0x7f07004c" />
    <public type="drawable" name="abc_list_divider_material" id="0x7f07004d" />
    <public type="drawable" name="abc_list_divider_mtrl_alpha" id="0x7f07004e" />
    <public type="drawable" name="abc_list_focused_holo" id="0x7f07004f" />
    <public type="drawable" name="abc_list_longpressed_holo" id="0x7f070050" />
    <public type="drawable" name="abc_list_pressed_holo_dark" id="0x7f070051" />
    <public type="drawable" name="abc_list_pressed_holo_light" id="0x7f070052" />
    <public type="drawable" name="abc_list_selector_background_transition_holo_dark" id="0x7f070053" />
    <public type="drawable" name="abc_list_selector_background_transition_holo_light" id="0x7f070054" />
    <public type="drawable" name="abc_list_selector_disabled_holo_dark" id="0x7f070055" />
    <public type="drawable" name="abc_list_selector_disabled_holo_light" id="0x7f070056" />
    <public type="drawable" name="abc_list_selector_holo_dark" id="0x7f070057" />
    <public type="drawable" name="abc_list_selector_holo_light" id="0x7f070058" />
    <public type="drawable" name="abc_menu_hardkey_panel_mtrl_mult" id="0x7f070059" />
    <public type="drawable" name="abc_popup_background_mtrl_mult" id="0x7f07005a" />
    <public type="drawable" name="abc_ratingbar_indicator_material" id="0x7f07005b" />
    <public type="drawable" name="abc_ratingbar_material" id="0x7f07005c" />
    <public type="drawable" name="abc_ratingbar_small_material" id="0x7f07005d" />
    <public type="drawable" name="abc_scrubber_control_off_mtrl_alpha" id="0x7f07005e" />
    <public type="drawable" name="abc_scrubber_control_to_pressed_mtrl_000" id="0x7f07005f" />
    <public type="drawable" name="abc_scrubber_control_to_pressed_mtrl_005" id="0x7f070060" />
    <public type="drawable" name="abc_scrubber_primary_mtrl_alpha" id="0x7f070061" />
    <public type="drawable" name="abc_scrubber_track_mtrl_alpha" id="0x7f070062" />
    <public type="drawable" name="abc_seekbar_thumb_material" id="0x7f070063" />
    <public type="drawable" name="abc_seekbar_tick_mark_material" id="0x7f070064" />
    <public type="drawable" name="abc_seekbar_track_material" id="0x7f070065" />
    <public type="drawable" name="abc_spinner_mtrl_am_alpha" id="0x7f070066" />
    <public type="drawable" name="abc_spinner_textfield_background_material" id="0x7f070067" />
    <public type="drawable" name="abc_star_black_48dp" id="0x7f070068" />
    <public type="drawable" name="abc_star_half_black_48dp" id="0x7f070069" />
    <public type="drawable" name="abc_switch_thumb_material" id="0x7f07006a" />
    <public type="drawable" name="abc_switch_track_mtrl_alpha" id="0x7f07006b" />
    <public type="drawable" name="abc_tab_indicator_material" id="0x7f07006c" />
    <public type="drawable" name="abc_tab_indicator_mtrl_alpha" id="0x7f07006d" />
    <public type="drawable" name="abc_text_cursor_material" id="0x7f07006e" />
    <public type="drawable" name="abc_text_select_handle_left_mtrl" id="0x7f07006f" />
    <public type="drawable" name="abc_text_select_handle_middle_mtrl" id="0x7f070070" />
    <public type="drawable" name="abc_text_select_handle_right_mtrl" id="0x7f070071" />
    <public type="drawable" name="abc_textfield_activated_mtrl_alpha" id="0x7f070072" />
    <public type="drawable" name="abc_textfield_default_mtrl_alpha" id="0x7f070073" />
    <public type="drawable" name="abc_textfield_search_activated_mtrl_alpha" id="0x7f070074" />
    <public type="drawable" name="abc_textfield_search_default_mtrl_alpha" id="0x7f070075" />
    <public type="drawable" name="abc_textfield_search_material" id="0x7f070076" />
    <public type="drawable" name="abc_vector_test" id="0x7f070077" />
    <public type="drawable" name="avd_hide_password" id="0x7f070078" />
    <public type="drawable" name="avd_show_password" id="0x7f070079" />
    <public type="drawable" name="banner" id="0x7f07007a" />
    <public type="drawable" name="banner_icon" id="0x7f07007b" />
    <public type="drawable" name="baseline_arrow_back_ios" id="0x7f07007c" />
    <public type="drawable" name="baseline_videocam_24" id="0x7f07007d" />
    <public type="drawable" name="btn_checkbox_checked_mtrl" id="0x7f07007e" />
    <public type="drawable" name="btn_checkbox_checked_to_unchecked_mtrl_animation" id="0x7f07007f" />
    <public type="drawable" name="btn_checkbox_unchecked_mtrl" id="0x7f070080" />
    <public type="drawable" name="btn_checkbox_unchecked_to_checked_mtrl_animation" id="0x7f070081" />
    <public type="drawable" name="btn_radio_off_mtrl" id="0x7f070082" />
    <public type="drawable" name="btn_radio_off_to_on_mtrl_animation" id="0x7f070083" />
    <public type="drawable" name="btn_radio_on_mtrl" id="0x7f070084" />
    <public type="drawable" name="btn_radio_on_to_off_mtrl_animation" id="0x7f070085" />
    <public type="drawable" name="camera_icon" id="0x7f070086" />
    <public type="drawable" name="chevron_up" id="0x7f070087" />
    <public type="drawable" name="close_btn" id="0x7f070088" />
    <public type="drawable" name="common_full_open_on_phone" id="0x7f070089" />
    <public type="drawable" name="common_google_signin_btn_icon_dark" id="0x7f07008a" />
    <public type="drawable" name="common_google_signin_btn_icon_dark_focused" id="0x7f07008b" />
    <public type="drawable" name="common_google_signin_btn_icon_dark_normal" id="0x7f07008c" />
    <public type="drawable" name="common_google_signin_btn_icon_dark_normal_background" id="0x7f07008d" />
    <public type="drawable" name="common_google_signin_btn_icon_disabled" id="0x7f07008e" />
    <public type="drawable" name="common_google_signin_btn_icon_light" id="0x7f07008f" />
    <public type="drawable" name="common_google_signin_btn_icon_light_focused" id="0x7f070090" />
    <public type="drawable" name="common_google_signin_btn_icon_light_normal" id="0x7f070091" />
    <public type="drawable" name="common_google_signin_btn_icon_light_normal_background" id="0x7f070092" />
    <public type="drawable" name="common_google_signin_btn_text_dark" id="0x7f070093" />
    <public type="drawable" name="common_google_signin_btn_text_dark_focused" id="0x7f070094" />
    <public type="drawable" name="common_google_signin_btn_text_dark_normal" id="0x7f070095" />
    <public type="drawable" name="common_google_signin_btn_text_dark_normal_background" id="0x7f070096" />
    <public type="drawable" name="common_google_signin_btn_text_disabled" id="0x7f070097" />
    <public type="drawable" name="common_google_signin_btn_text_light" id="0x7f070098" />
    <public type="drawable" name="common_google_signin_btn_text_light_focused" id="0x7f070099" />
    <public type="drawable" name="common_google_signin_btn_text_light_normal" id="0x7f07009a" />
    <public type="drawable" name="common_google_signin_btn_text_light_normal_background" id="0x7f07009b" />
    <public type="drawable" name="compat_splash_screen_no_icon_background" id="0x7f07009d" />
    <public type="drawable" name="connect_widget_icon" id="0x7f07009e" />
    <public type="drawable" name="design_fab_background" id="0x7f07009f" />
    <public type="drawable" name="design_ic_visibility" id="0x7f0700a0" />
    <public type="drawable" name="design_ic_visibility_off" id="0x7f0700a1" />
    <public type="drawable" name="design_password_eye" id="0x7f0700a2" />
    <public type="drawable" name="design_snackbar_background" id="0x7f0700a3" />
    <public type="drawable" name="files_icon" id="0x7f0700a4" />
    <public type="drawable" name="googleg_disabled_color_18" id="0x7f0700a5" />
    <public type="drawable" name="googleg_standard_color_18" id="0x7f0700a6" />
    <public type="drawable" name="ic_mtrl_checked_circle" id="0x7f0700b6" />
    <public type="drawable" name="ic_mtrl_chip_checked_black" id="0x7f0700b7" />
    <public type="drawable" name="ic_mtrl_chip_checked_circle" id="0x7f0700b8" />
    <public type="drawable" name="ic_mtrl_chip_close_circle" id="0x7f0700b9" />
    <public type="drawable" name="ic_os_notification_fallback_white_24dp" id="0x7f0700ba" />
    <public type="drawable" name="ic_stat_onesignal_default" id="0x7f0700bc" />
    <public type="drawable" name="icon_background" id="0x7f0700bd" />
    <public type="drawable" name="indeterminate_static" id="0x7f0700be" />
    <public type="drawable" name="mail_image" id="0x7f0700ca" />
    <public type="drawable" name="material_cursor_drawable" id="0x7f0700cb" />
    <public type="drawable" name="material_ic_calendar_black_24dp" id="0x7f0700cc" />
    <public type="drawable" name="material_ic_clear_black_24dp" id="0x7f0700cd" />
    <public type="drawable" name="material_ic_edit_black_24dp" id="0x7f0700ce" />
    <public type="drawable" name="material_ic_keyboard_arrow_left_black_24dp" id="0x7f0700cf" />
    <public type="drawable" name="material_ic_keyboard_arrow_next_black_24dp" id="0x7f0700d0" />
    <public type="drawable" name="material_ic_keyboard_arrow_previous_black_24dp" id="0x7f0700d1" />
    <public type="drawable" name="material_ic_keyboard_arrow_right_black_24dp" id="0x7f0700d2" />
    <public type="drawable" name="material_ic_menu_arrow_down_black_24dp" id="0x7f0700d3" />
    <public type="drawable" name="mtrl_checkbox_button" id="0x7f0700d6" />
    <public type="drawable" name="mtrl_checkbox_button_checked_unchecked" id="0x7f0700d7" />
    <public type="drawable" name="mtrl_checkbox_button_icon" id="0x7f0700d8" />
    <public type="drawable" name="mtrl_checkbox_button_icon_checked_indeterminate" id="0x7f0700d9" />
    <public type="drawable" name="mtrl_checkbox_button_icon_checked_unchecked" id="0x7f0700da" />
    <public type="drawable" name="mtrl_checkbox_button_icon_indeterminate_checked" id="0x7f0700db" />
    <public type="drawable" name="mtrl_checkbox_button_icon_indeterminate_unchecked" id="0x7f0700dc" />
    <public type="drawable" name="mtrl_checkbox_button_icon_unchecked_checked" id="0x7f0700dd" />
    <public type="drawable" name="mtrl_checkbox_button_icon_unchecked_indeterminate" id="0x7f0700de" />
    <public type="drawable" name="mtrl_checkbox_button_unchecked_checked" id="0x7f0700df" />
    <public type="drawable" name="mtrl_dialog_background" id="0x7f0700e0" />
    <public type="drawable" name="mtrl_dropdown_arrow" id="0x7f0700e1" />
    <public type="drawable" name="mtrl_ic_arrow_drop_down" id="0x7f0700e2" />
    <public type="drawable" name="mtrl_ic_arrow_drop_up" id="0x7f0700e3" />
    <public type="drawable" name="mtrl_ic_cancel" id="0x7f0700e4" />
    <public type="drawable" name="mtrl_ic_check_mark" id="0x7f0700e5" />
    <public type="drawable" name="mtrl_ic_checkbox_checked" id="0x7f0700e6" />
    <public type="drawable" name="mtrl_ic_checkbox_unchecked" id="0x7f0700e7" />
    <public type="drawable" name="mtrl_ic_error" id="0x7f0700e8" />
    <public type="drawable" name="mtrl_ic_indeterminate" id="0x7f0700e9" />
    <public type="drawable" name="mtrl_navigation_bar_item_background" id="0x7f0700ea" />
    <public type="drawable" name="mtrl_popupmenu_background" id="0x7f0700eb" />
    <public type="drawable" name="mtrl_popupmenu_background_overlay" id="0x7f0700ec" />
    <public type="drawable" name="mtrl_tabs_default_indicator" id="0x7f0700f9" />
    <public type="drawable" name="navigation_empty_icon" id="0x7f0700fa" />
    <public type="drawable" name="notification" id="0x7f0700fb" />
    <public type="drawable" name="notification_action_background" id="0x7f0700fc" />
    <public type="drawable" name="notification_bg" id="0x7f0700fd" />
    <public type="drawable" name="notification_bg_low" id="0x7f0700fe" />
    <public type="drawable" name="notification_bg_low_normal" id="0x7f0700ff" />
    <public type="drawable" name="notification_bg_low_pressed" id="0x7f070100" />
    <public type="drawable" name="notification_bg_normal" id="0x7f070101" />
    <public type="drawable" name="notification_bg_normal_pressed" id="0x7f070102" />
    <public type="drawable" name="notification_icon" id="0x7f070103" />
    <public type="drawable" name="notification_icon_background" id="0x7f070104" />
    <public type="drawable" name="notification_oversize_large_icon_bg" id="0x7f070105" />
    <public type="drawable" name="notification_template_icon_bg" id="0x7f070106" />
    <public type="drawable" name="notification_template_icon_low_bg" id="0x7f070107" />
    <public type="drawable" name="notification_tile_bg" id="0x7f070108" />
    <public type="drawable" name="notify_panel_notification_icon_bg" id="0x7f070109" />
    <public type="drawable" name="splashicon" id="0x7f07010a" />
    <public type="drawable" name="splashimgbackground" id="0x7f07010b" />
    <public type="drawable" name="test_level_drawable" id="0x7f07010c" />
    <public type="drawable" name="tooltip_frame_dark" id="0x7f07010d" />
    <public type="drawable" name="tooltip_frame_light" id="0x7f07010e" />
    <public type="font" name="fontawesome_font_v5_9_0" id="0x7f080000" />
    <public type="id" name="ALT" id="0x7f090000" />
    <public type="id" name="BOTTOM_END" id="0x7f090001" />
    <public type="id" name="BOTTOM_START" id="0x7f090002" />
    <public type="id" name="CTRL" id="0x7f090003" />
    <public type="id" name="FUNCTION" id="0x7f090004" />
    <public type="id" name="META" id="0x7f090005" />
    <public type="id" name="NO_DEBUG" id="0x7f090006" />
    <public type="id" name="SHIFT" id="0x7f090007" />
    <public type="id" name="SHOW_ALL" id="0x7f090008" />
    <public type="id" name="SHOW_PATH" id="0x7f090009" />
    <public type="id" name="SHOW_PROGRESS" id="0x7f09000a" />
    <public type="id" name="SYM" id="0x7f09000b" />
    <public type="id" name="TOP_END" id="0x7f09000c" />
    <public type="id" name="TOP_START" id="0x7f09000d" />
    <public type="id" name="accelerate" id="0x7f09000e" />
    <public type="id" name="accessibility_action_clickable_span" id="0x7f09000f" />
    <public type="id" name="accessibility_custom_action_0" id="0x7f090010" />
    <public type="id" name="accessibility_custom_action_1" id="0x7f090011" />
    <public type="id" name="accessibility_custom_action_10" id="0x7f090012" />
    <public type="id" name="accessibility_custom_action_11" id="0x7f090013" />
    <public type="id" name="accessibility_custom_action_12" id="0x7f090014" />
    <public type="id" name="accessibility_custom_action_13" id="0x7f090015" />
    <public type="id" name="accessibility_custom_action_14" id="0x7f090016" />
    <public type="id" name="accessibility_custom_action_15" id="0x7f090017" />
    <public type="id" name="accessibility_custom_action_16" id="0x7f090018" />
    <public type="id" name="accessibility_custom_action_17" id="0x7f090019" />
    <public type="id" name="accessibility_custom_action_18" id="0x7f09001a" />
    <public type="id" name="accessibility_custom_action_19" id="0x7f09001b" />
    <public type="id" name="accessibility_custom_action_2" id="0x7f09001c" />
    <public type="id" name="accessibility_custom_action_20" id="0x7f09001d" />
    <public type="id" name="accessibility_custom_action_21" id="0x7f09001e" />
    <public type="id" name="accessibility_custom_action_22" id="0x7f09001f" />
    <public type="id" name="accessibility_custom_action_23" id="0x7f090020" />
    <public type="id" name="accessibility_custom_action_24" id="0x7f090021" />
    <public type="id" name="accessibility_custom_action_25" id="0x7f090022" />
    <public type="id" name="accessibility_custom_action_26" id="0x7f090023" />
    <public type="id" name="accessibility_custom_action_27" id="0x7f090024" />
    <public type="id" name="accessibility_custom_action_28" id="0x7f090025" />
    <public type="id" name="accessibility_custom_action_29" id="0x7f090026" />
    <public type="id" name="accessibility_custom_action_3" id="0x7f090027" />
    <public type="id" name="accessibility_custom_action_30" id="0x7f090028" />
    <public type="id" name="accessibility_custom_action_31" id="0x7f090029" />
    <public type="id" name="accessibility_custom_action_4" id="0x7f09002a" />
    <public type="id" name="accessibility_custom_action_5" id="0x7f09002b" />
    <public type="id" name="accessibility_custom_action_6" id="0x7f09002c" />
    <public type="id" name="accessibility_custom_action_7" id="0x7f09002d" />
    <public type="id" name="accessibility_custom_action_8" id="0x7f09002e" />
    <public type="id" name="accessibility_custom_action_9" id="0x7f09002f" />
    <public type="id" name="actionDown" id="0x7f090030" />
    <public type="id" name="actionDownUp" id="0x7f090031" />
    <public type="id" name="actionUp" id="0x7f090032" />
    <public type="id" name="action_bar" id="0x7f090033" />
    <public type="id" name="action_bar_activity_content" id="0x7f090034" />
    <public type="id" name="action_bar_container" id="0x7f090035" />
    <public type="id" name="action_bar_root" id="0x7f090036" />
    <public type="id" name="action_bar_spinner" id="0x7f090037" />
    <public type="id" name="action_bar_subtitle" id="0x7f090038" />
    <public type="id" name="action_bar_title" id="0x7f090039" />
    <public type="id" name="action_container" id="0x7f09003a" />
    <public type="id" name="action_context_bar" id="0x7f09003b" />
    <public type="id" name="action_divider" id="0x7f09003c" />
    <public type="id" name="action_image" id="0x7f09003d" />
    <public type="id" name="action_menu_divider" id="0x7f09003e" />
    <public type="id" name="action_menu_presenter" id="0x7f09003f" />
    <public type="id" name="action_mode_bar" id="0x7f090040" />
    <public type="id" name="action_mode_bar_stub" id="0x7f090041" />
    <public type="id" name="action_mode_close_button" id="0x7f090042" />
    <public type="id" name="action_text" id="0x7f090043" />
    <public type="id" name="actions" id="0x7f090044" />
    <public type="id" name="activity_chooser_view_content" id="0x7f090045" />
    <public type="id" name="add" id="0x7f090046" />
    <public type="id" name="adjust_height" id="0x7f090047" />
    <public type="id" name="adjust_width" id="0x7f090048" />
    <public type="id" name="alertTitle" id="0x7f090049" />
    <public type="id" name="aligned" id="0x7f09004a" />
    <public type="id" name="all" id="0x7f09004b" />
    <public type="id" name="allStates" id="0x7f09004c" />
    <public type="id" name="always" id="0x7f09004d" />
    <public type="id" name="androidx_compose_ui_view_composition_context" id="0x7f09004e" />
    <public type="id" name="animateToEnd" id="0x7f09004f" />
    <public type="id" name="animateToStart" id="0x7f090050" />
    <public type="id" name="antiClockwise" id="0x7f090051" />
    <public type="id" name="anticipate" id="0x7f090052" />
    <public type="id" name="arc" id="0x7f090053" />
    <public type="id" name="asConfigured" id="0x7f090054" />
    <public type="id" name="async" id="0x7f090055" />
    <public type="id" name="auto" id="0x7f090056" />
    <public type="id" name="autoComplete" id="0x7f090057" />
    <public type="id" name="autoCompleteToEnd" id="0x7f090058" />
    <public type="id" name="autoCompleteToStart" id="0x7f090059" />
    <public type="id" name="automatic" id="0x7f09005a" />
    <public type="id" name="barrier" id="0x7f09005b" />
    <public type="id" name="baseline" id="0x7f09005c" />
    <public type="id" name="beginOnFirstDraw" id="0x7f09005d" />
    <public type="id" name="beginning" id="0x7f09005e" />
    <public type="id" name="bestChoice" id="0x7f09005f" />
    <public type="id" name="blocking" id="0x7f090060" />
    <public type="id" name="bottom" id="0x7f090061" />
    <public type="id" name="bounce" id="0x7f090062" />
    <public type="id" name="bounceBoth" id="0x7f090063" />
    <public type="id" name="bounceEnd" id="0x7f090064" />
    <public type="id" name="bounceStart" id="0x7f090065" />
    <public type="id" name="browser_actions_header_text" id="0x7f090066" />
    <public type="id" name="browser_actions_menu_item_icon" id="0x7f090067" />
    <public type="id" name="browser_actions_menu_item_text" id="0x7f090068" />
    <public type="id" name="browser_actions_menu_items" id="0x7f090069" />
    <public type="id" name="browser_actions_menu_view" id="0x7f09006a" />
    <public type="id" name="buttonPanel" id="0x7f09006b" />
    <public type="id" name="cache_measures" id="0x7f09006c" />
    <public type="id" name="callMeasure" id="0x7f09006d" />
    <public type="id" name="cancel_button" id="0x7f09006e" />
    <public type="id" name="carryVelocity" id="0x7f09006f" />
    <public type="id" name="center" id="0x7f090070" />
    <public type="id" name="centerCrop" id="0x7f090071" />
    <public type="id" name="centerInside" id="0x7f090072" />
    <public type="id" name="center_horizontal" id="0x7f090073" />
    <public type="id" name="center_vertical" id="0x7f090074" />
    <public type="id" name="chain" id="0x7f090075" />
    <public type="id" name="chain2" id="0x7f090076" />
    <public type="id" name="chains" id="0x7f090077" />
    <public type="id" name="checkbox" id="0x7f090078" />
    <public type="id" name="checked" id="0x7f090079" />
    <public type="id" name="chronometer" id="0x7f09007a" />
    <public type="id" name="circle_center" id="0x7f09007b" />
    <public type="id" name="clear_text" id="0x7f09007c" />
    <public type="id" name="clip_horizontal" id="0x7f09007d" />
    <public type="id" name="clip_vertical" id="0x7f09007e" />
    <public type="id" name="clockwise" id="0x7f09007f" />
    <public type="id" name="closest" id="0x7f090080" />
    <public type="id" name="coil_request_manager" id="0x7f090081" />
    <public type="id" name="collapseActionView" id="0x7f090082" />
    <public type="id" name="compose_view_saveable_id_tag" id="0x7f090083" />
    <public type="id" name="compress" id="0x7f090084" />
    <public type="id" name="confirm_button" id="0x7f090085" />
    <public type="id" name="constraint" id="0x7f090086" />
    <public type="id" name="consume_window_insets_tag" id="0x7f090087" />
    <public type="id" name="container" id="0x7f090088" />
    <public type="id" name="content" id="0x7f090089" />
    <public type="id" name="contentPanel" id="0x7f09008a" />
    <public type="id" name="contiguous" id="0x7f09008b" />
    <public type="id" name="continuousVelocity" id="0x7f09008c" />
    <public type="id" name="coordinator" id="0x7f09008d" />
    <public type="id" name="cos" id="0x7f09008e" />
    <public type="id" name="counterclockwise" id="0x7f09008f" />
    <public type="id" name="cradle" id="0x7f090090" />
    <public type="id" name="currentState" id="0x7f090091" />
    <public type="id" name="custom" id="0x7f090092" />
    <public type="id" name="customPanel" id="0x7f090093" />
    <public type="id" name="cut" id="0x7f090094" />
    <public type="id" name="dark" id="0x7f090095" />
    <public type="id" name="date_picker_actions" id="0x7f090096" />
    <public type="id" name="decelerate" id="0x7f090097" />
    <public type="id" name="decelerateAndComplete" id="0x7f090098" />
    <public type="id" name="decor_content_parent" id="0x7f090099" />
    <public type="id" name="default_activity_button" id="0x7f09009a" />
    <public type="id" name="deltaRelative" id="0x7f09009b" />
    <public type="id" name="dependency_ordering" id="0x7f09009c" />
    <public type="id" name="design_bottom_sheet" id="0x7f09009d" />
    <public type="id" name="design_menu_item_action_area" id="0x7f09009e" />
    <public type="id" name="design_menu_item_action_area_stub" id="0x7f09009f" />
    <public type="id" name="design_menu_item_text" id="0x7f0900a0" />
    <public type="id" name="design_navigation_view" id="0x7f0900a1" />
    <public type="id" name="dialog_button" id="0x7f0900a2" />
    <public type="id" name="dimensions" id="0x7f0900a3" />
    <public type="id" name="direct" id="0x7f0900a4" />
    <public type="id" name="disableHome" id="0x7f0900a5" />
    <public type="id" name="disableIntraAutoTransition" id="0x7f0900a6" />
    <public type="id" name="disablePostScroll" id="0x7f0900a7" />
    <public type="id" name="disableScroll" id="0x7f0900a8" />
    <public type="id" name="disabled" id="0x7f0900a9" />
    <public type="id" name="disjoint" id="0x7f0900aa" />
    <public type="id" name="dragAnticlockwise" id="0x7f0900ab" />
    <public type="id" name="dragClockwise" id="0x7f0900ac" />
    <public type="id" name="dragDown" id="0x7f0900ad" />
    <public type="id" name="dragEnd" id="0x7f0900ae" />
    <public type="id" name="dragLeft" id="0x7f0900af" />
    <public type="id" name="dragRight" id="0x7f0900b0" />
    <public type="id" name="dragStart" id="0x7f0900b1" />
    <public type="id" name="dragUp" id="0x7f0900b2" />
    <public type="id" name="dropdown_menu" id="0x7f0900b3" />
    <public type="id" name="easeIn" id="0x7f0900b4" />
    <public type="id" name="easeInOut" id="0x7f0900b5" />
    <public type="id" name="easeOut" id="0x7f0900b6" />
    <public type="id" name="east" id="0x7f0900b7" />
    <public type="id" name="edge" id="0x7f0900b8" />
    <public type="id" name="edit_query" id="0x7f0900b9" />
    <public type="id" name="edit_text_id" id="0x7f0900ba" />
    <public type="id" name="elastic" id="0x7f0900bb" />
    <public type="id" name="embed" id="0x7f0900bc" />
    <public type="id" name="enabled" id="0x7f0900bd" />
    <public type="id" name="end" id="0x7f0900be" />
    <public type="id" name="endToStart" id="0x7f0900bf" />
    <public type="id" name="enterAlways" id="0x7f0900c0" />
    <public type="id" name="enterAlwaysCollapsed" id="0x7f0900c1" />
    <public type="id" name="escape" id="0x7f0900c2" />
    <public type="id" name="exitUntilCollapsed" id="0x7f0900c3" />
    <public type="id" name="expand_activities_button" id="0x7f0900c4" />
    <public type="id" name="expanded_menu" id="0x7f0900c5" />
    <public type="id" name="exturls_webview" id="0x7f0900c6" />
    <public type="id" name="fade" id="0x7f0900c7" />
    <public type="id" name="fill" id="0x7f0900c8" />
    <public type="id" name="fill_horizontal" id="0x7f0900c9" />
    <public type="id" name="fill_vertical" id="0x7f0900ca" />
    <public type="id" name="filled" id="0x7f0900cb" />
    <public type="id" name="fitCenter" id="0x7f0900cc" />
    <public type="id" name="fitEnd" id="0x7f0900cd" />
    <public type="id" name="fitStart" id="0x7f0900ce" />
    <public type="id" name="fitToContents" id="0x7f0900cf" />
    <public type="id" name="fitXY" id="0x7f0900d0" />
    <public type="id" name="fixed" id="0x7f0900d1" />
    <public type="id" name="flip" id="0x7f0900d2" />
    <public type="id" name="floating" id="0x7f0900d3" />
    <public type="id" name="forever" id="0x7f0900d4" />
    <public type="id" name="fragment_container_view_tag" id="0x7f0900d5" />
    <public type="id" name="frost" id="0x7f0900d6" />
    <public type="id" name="fullscreen_header" id="0x7f0900d7" />
    <public type="id" name="ghost_view" id="0x7f0900d8" />
    <public type="id" name="ghost_view_holder" id="0x7f0900d9" />
    <public type="id" name="gone" id="0x7f0900da" />
    <public type="id" name="graph" id="0x7f0900db" />
    <public type="id" name="graph_wrap" id="0x7f0900dc" />
    <public type="id" name="group_divider" id="0x7f0900dd" />
    <public type="id" name="grouping" id="0x7f0900de" />
    <public type="id" name="groups" id="0x7f0900df" />
    <public type="id" name="hardware" id="0x7f0900e0" />
    <public type="id" name="header_title" id="0x7f0900e1" />
    <public type="id" name="hide_ime_id" id="0x7f0900e2" />
    <public type="id" name="hide_in_inspector_tag" id="0x7f0900e3" />
    <public type="id" name="hideable" id="0x7f0900e4" />
    <public type="id" name="home" id="0x7f0900e5" />
    <public type="id" name="homeAsUp" id="0x7f0900e6" />
    <public type="id" name="honorRequest" id="0x7f0900e7" />
    <public type="id" name="horizontal_only" id="0x7f0900e8" />
    <public type="id" name="icon" id="0x7f0900e9" />
    <public type="id" name="icon_group" id="0x7f0900ea" />
    <public type="id" name="icon_only" id="0x7f0900eb" />
    <public type="id" name="iconics_tag_id" id="0x7f0900ec" />
    <public type="id" name="ifRoom" id="0x7f0900ed" />
    <public type="id" name="ignore" id="0x7f0900ee" />
    <public type="id" name="ignoreRequest" id="0x7f0900ef" />
    <public type="id" name="image" id="0x7f0900f0" />
    <public type="id" name="immediateStop" id="0x7f0900f1" />
    <public type="id" name="included" id="0x7f0900f2" />
    <public type="id" name="indeterminate" id="0x7f0900f3" />
    <public type="id" name="info" id="0x7f0900f4" />
    <public type="id" name="inspection_slot_table_set" id="0x7f0900f5" />
    <public type="id" name="invisible" id="0x7f0900f6" />
    <public type="id" name="inward" id="0x7f0900f7" />
    <public type="id" name="is_pooling_container_tag" id="0x7f0900f8" />
    <public type="id" name="italic" id="0x7f0900f9" />
    <public type="id" name="item_touch_helper_previous_elevation" id="0x7f0900fa" />
    <public type="id" name="jumpToEnd" id="0x7f0900fb" />
    <public type="id" name="jumpToStart" id="0x7f0900fc" />
    <public type="id" name="labeled" id="0x7f0900fd" />
    <public type="id" name="layout" id="0x7f0900fe" />
    <public type="id" name="left" id="0x7f0900ff" />
    <public type="id" name="leftToRight" id="0x7f090100" />
    <public type="id" name="legacy" id="0x7f090101" />
    <public type="id" name="light" id="0x7f090102" />
    <public type="id" name="line1" id="0x7f090103" />
    <public type="id" name="line3" id="0x7f090104" />
    <public type="id" name="linear" id="0x7f090105" />
    <public type="id" name="listMode" id="0x7f090106" />
    <public type="id" name="list_item" id="0x7f090107" />
    <public type="id" name="lottie_layer_name" id="0x7f090108" />
    <public type="id" name="m3_side_sheet" id="0x7f090109" />
    <public type="id" name="marquee" id="0x7f09010a" />
    <public type="id" name="masked" id="0x7f09010b" />
    <public type="id" name="match_constraint" id="0x7f09010c" />
    <public type="id" name="match_parent" id="0x7f09010d" />
    <public type="id" name="material_clock_display" id="0x7f09010e" />
    <public type="id" name="material_clock_display_and_toggle" id="0x7f09010f" />
    <public type="id" name="material_clock_face" id="0x7f090110" />
    <public type="id" name="material_clock_hand" id="0x7f090111" />
    <public type="id" name="material_clock_level" id="0x7f090112" />
    <public type="id" name="material_clock_period_am_button" id="0x7f090113" />
    <public type="id" name="material_clock_period_pm_button" id="0x7f090114" />
    <public type="id" name="material_clock_period_toggle" id="0x7f090115" />
    <public type="id" name="material_hour_text_input" id="0x7f090116" />
    <public type="id" name="material_hour_tv" id="0x7f090117" />
    <public type="id" name="material_label" id="0x7f090118" />
    <public type="id" name="material_minute_text_input" id="0x7f090119" />
    <public type="id" name="material_minute_tv" id="0x7f09011a" />
    <public type="id" name="material_textinput_timepicker" id="0x7f09011b" />
    <public type="id" name="material_timepicker_cancel_button" id="0x7f09011c" />
    <public type="id" name="material_timepicker_container" id="0x7f09011d" />
    <public type="id" name="material_timepicker_mode_button" id="0x7f09011e" />
    <public type="id" name="material_timepicker_ok_button" id="0x7f09011f" />
    <public type="id" name="material_timepicker_view" id="0x7f090120" />
    <public type="id" name="material_value_index" id="0x7f090121" />
    <public type="id" name="matrix" id="0x7f090122" />
    <public type="id" name="message" id="0x7f090123" />
    <public type="id" name="middle" id="0x7f090124" />
    <public type="id" name="mini" id="0x7f090125" />
    <public type="id" name="month_grid" id="0x7f090126" />
    <public type="id" name="month_navigation_bar" id="0x7f090127" />
    <public type="id" name="month_navigation_fragment_toggle" id="0x7f090128" />
    <public type="id" name="month_navigation_next" id="0x7f090129" />
    <public type="id" name="month_navigation_previous" id="0x7f09012a" />
    <public type="id" name="month_title" id="0x7f09012b" />
    <public type="id" name="motion_base" id="0x7f09012c" />
    <public type="id" name="mtrl_anchor_parent" id="0x7f09012d" />
    <public type="id" name="mtrl_calendar_day_selector_frame" id="0x7f09012e" />
    <public type="id" name="mtrl_calendar_days_of_week" id="0x7f09012f" />
    <public type="id" name="mtrl_calendar_frame" id="0x7f090130" />
    <public type="id" name="mtrl_calendar_main_pane" id="0x7f090131" />
    <public type="id" name="mtrl_calendar_months" id="0x7f090132" />
    <public type="id" name="mtrl_calendar_selection_frame" id="0x7f090133" />
    <public type="id" name="mtrl_calendar_text_input_frame" id="0x7f090134" />
    <public type="id" name="mtrl_calendar_year_selector_frame" id="0x7f090135" />
    <public type="id" name="mtrl_card_checked_layer_id" id="0x7f090136" />
    <public type="id" name="mtrl_child_content_container" id="0x7f090137" />
    <public type="id" name="mtrl_internal_children_alpha_tag" id="0x7f090138" />
    <public type="id" name="mtrl_motion_snapshot_view" id="0x7f090139" />
    <public type="id" name="mtrl_picker_fullscreen" id="0x7f09013a" />
    <public type="id" name="mtrl_picker_header" id="0x7f09013b" />
    <public type="id" name="mtrl_picker_header_selection_text" id="0x7f09013c" />
    <public type="id" name="mtrl_picker_header_title_and_selection" id="0x7f09013d" />
    <public type="id" name="mtrl_picker_header_toggle" id="0x7f09013e" />
    <public type="id" name="mtrl_picker_text_input_date" id="0x7f09013f" />
    <public type="id" name="mtrl_picker_text_input_range_end" id="0x7f090140" />
    <public type="id" name="mtrl_picker_text_input_range_start" id="0x7f090141" />
    <public type="id" name="mtrl_picker_title_text" id="0x7f090142" />
    <public type="id" name="mtrl_view_tag_bottom_padding" id="0x7f090143" />
    <public type="id" name="multiply" id="0x7f090144" />
    <public type="id" name="nav_controller_view_tag" id="0x7f090145" />
    <public type="id" name="navigation_bar_item_active_indicator_view" id="0x7f090146" />
    <public type="id" name="navigation_bar_item_icon_container" id="0x7f090147" />
    <public type="id" name="navigation_bar_item_icon_view" id="0x7f090148" />
    <public type="id" name="navigation_bar_item_labels_group" id="0x7f090149" />
    <public type="id" name="navigation_bar_item_large_label_view" id="0x7f09014a" />
    <public type="id" name="navigation_bar_item_small_label_view" id="0x7f09014b" />
    <public type="id" name="navigation_header_container" id="0x7f09014c" />
    <public type="id" name="never" id="0x7f09014d" />
    <public type="id" name="neverCompleteToEnd" id="0x7f09014e" />
    <public type="id" name="neverCompleteToStart" id="0x7f09014f" />
    <public type="id" name="noScroll" id="0x7f090150" />
    <public type="id" name="noState" id="0x7f090151" />
    <public type="id" name="none" id="0x7f090152" />
    <public type="id" name="normal" id="0x7f090153" />
    <public type="id" name="north" id="0x7f090154" />
    <public type="id" name="notification_background" id="0x7f090155" />
    <public type="id" name="notification_main_column" id="0x7f090156" />
    <public type="id" name="notification_main_column_container" id="0x7f090157" />
    <public type="id" name="off" id="0x7f090158" />
    <public type="id" name="on" id="0x7f090159" />
    <public type="id" name="onInterceptTouchReturnSwipe" id="0x7f09015a" />
    <public type="id" name="open_search_bar_text_view" id="0x7f09015b" />
    <public type="id" name="open_search_view_background" id="0x7f09015c" />
    <public type="id" name="open_search_view_clear_button" id="0x7f09015d" />
    <public type="id" name="open_search_view_content_container" id="0x7f09015e" />
    <public type="id" name="open_search_view_divider" id="0x7f09015f" />
    <public type="id" name="open_search_view_dummy_toolbar" id="0x7f090160" />
    <public type="id" name="open_search_view_edit_text" id="0x7f090161" />
    <public type="id" name="open_search_view_header_container" id="0x7f090162" />
    <public type="id" name="open_search_view_root" id="0x7f090163" />
    <public type="id" name="open_search_view_scrim" id="0x7f090164" />
    <public type="id" name="open_search_view_search_prefix" id="0x7f090165" />
    <public type="id" name="open_search_view_status_bar_spacer" id="0x7f090166" />
    <public type="id" name="open_search_view_toolbar" id="0x7f090167" />
    <public type="id" name="open_search_view_toolbar_container" id="0x7f090168" />
    <public type="id" name="os_bgimage_notif_bgimage" id="0x7f090169" />
    <public type="id" name="os_bgimage_notif_bgimage_align_layout" id="0x7f09016a" />
    <public type="id" name="os_bgimage_notif_bgimage_right_aligned" id="0x7f09016b" />
    <public type="id" name="os_bgimage_notif_body" id="0x7f09016c" />
    <public type="id" name="os_bgimage_notif_title" id="0x7f09016d" />
    <public type="id" name="outline" id="0x7f09016e" />
    <public type="id" name="outward" id="0x7f09016f" />
    <public type="id" name="overshoot" id="0x7f090170" />
    <public type="id" name="packed" id="0x7f090171" />
    <public type="id" name="parallax" id="0x7f090172" />
    <public type="id" name="parent" id="0x7f090173" />
    <public type="id" name="parentPanel" id="0x7f090174" />
    <public type="id" name="parentRelative" id="0x7f090175" />
    <public type="id" name="parent_matrix" id="0x7f090176" />
    <public type="id" name="password_toggle" id="0x7f090177" />
    <public type="id" name="path" id="0x7f090178" />
    <public type="id" name="pathRelative" id="0x7f090179" />
    <public type="id" name="peekHeight" id="0x7f09017a" />
    <public type="id" name="percent" id="0x7f09017b" />
    <public type="id" name="pin" id="0x7f09017c" />
    <public type="id" name="pooling_container_listener_holder_tag" id="0x7f09017d" />
    <public type="id" name="position" id="0x7f09017e" />
    <public type="id" name="postLayout" id="0x7f09017f" />
    <public type="id" name="pressed" id="0x7f090180" />
    <public type="id" name="progress_circular" id="0x7f090181" />
    <public type="id" name="progress_horizontal" id="0x7f090182" />
    <public type="id" name="radio" id="0x7f090183" />
    <public type="id" name="ratio" id="0x7f090184" />
    <public type="id" name="rectangles" id="0x7f090185" />
    <public type="id" name="report_drawn" id="0x7f090186" />
    <public type="id" name="restart" id="0x7f090187" />
    <public type="id" name="reverse" id="0x7f090188" />
    <public type="id" name="reverseSawtooth" id="0x7f090189" />
    <public type="id" name="right" id="0x7f09018a" />
    <public type="id" name="rightToLeft" id="0x7f09018b" />
    <public type="id" name="right_icon" id="0x7f09018c" />
    <public type="id" name="right_side" id="0x7f09018d" />
    <public type="id" name="rounded" id="0x7f09018e" />
    <public type="id" name="row_index_key" id="0x7f09018f" />
    <public type="id" name="save_non_transition_alpha" id="0x7f090190" />
    <public type="id" name="save_overlay_view" id="0x7f090191" />
    <public type="id" name="sawtooth" id="0x7f090192" />
    <public type="id" name="scale" id="0x7f090193" />
    <public type="id" name="screen" id="0x7f090194" />
    <public type="id" name="scroll" id="0x7f090195" />
    <public type="id" name="scrollIndicatorDown" id="0x7f090196" />
    <public type="id" name="scrollIndicatorUp" id="0x7f090197" />
    <public type="id" name="scrollView" id="0x7f090198" />
    <public type="id" name="scrollable" id="0x7f090199" />
    <public type="id" name="search_badge" id="0x7f09019a" />
    <public type="id" name="search_bar" id="0x7f09019b" />
    <public type="id" name="search_button" id="0x7f09019c" />
    <public type="id" name="search_close_btn" id="0x7f09019d" />
    <public type="id" name="search_edit_frame" id="0x7f09019e" />
    <public type="id" name="search_go_btn" id="0x7f09019f" />
    <public type="id" name="search_mag_icon" id="0x7f0901a0" />
    <public type="id" name="search_plate" id="0x7f0901a1" />
    <public type="id" name="search_src_text" id="0x7f0901a2" />
    <public type="id" name="search_voice_btn" id="0x7f0901a3" />
    <public type="id" name="select_dialog_listview" id="0x7f0901a4" />
    <public type="id" name="selected" id="0x7f0901a5" />
    <public type="id" name="selection_type" id="0x7f0901a6" />
    <public type="id" name="sharedValueSet" id="0x7f0901a7" />
    <public type="id" name="sharedValueUnset" id="0x7f0901a8" />
    <public type="id" name="shortcut" id="0x7f0901a9" />
    <public type="id" name="showCustom" id="0x7f0901aa" />
    <public type="id" name="showHome" id="0x7f0901ab" />
    <public type="id" name="showTitle" id="0x7f0901ac" />
    <public type="id" name="sin" id="0x7f0901ad" />
    <public type="id" name="skipCollapsed" id="0x7f0901ae" />
    <public type="id" name="skipped" id="0x7f0901af" />
    <public type="id" name="slide" id="0x7f0901b0" />
    <public type="id" name="snackbar_action" id="0x7f0901b1" />
    <public type="id" name="snackbar_text" id="0x7f0901b2" />
    <public type="id" name="snap" id="0x7f0901b3" />
    <public type="id" name="snapMargins" id="0x7f0901b4" />
    <public type="id" name="software" id="0x7f0901b5" />
    <public type="id" name="south" id="0x7f0901b6" />
    <public type="id" name="spacer" id="0x7f0901b7" />
    <public type="id" name="special_effects_controller_view_tag" id="0x7f0901b8" />
    <public type="id" name="splashscreen_icon_view" id="0x7f0901b9" />
    <public type="id" name="spline" id="0x7f0901ba" />
    <public type="id" name="split_action_bar" id="0x7f0901bb" />
    <public type="id" name="spread" id="0x7f0901bc" />
    <public type="id" name="spread_inside" id="0x7f0901bd" />
    <public type="id" name="spring" id="0x7f0901be" />
    <public type="id" name="square" id="0x7f0901bf" />
    <public type="id" name="src_atop" id="0x7f0901c0" />
    <public type="id" name="src_in" id="0x7f0901c1" />
    <public type="id" name="src_over" id="0x7f0901c2" />
    <public type="id" name="standard" id="0x7f0901c3" />
    <public type="id" name="start" id="0x7f0901c4" />
    <public type="id" name="startHorizontal" id="0x7f0901c5" />
    <public type="id" name="startToEnd" id="0x7f0901c6" />
    <public type="id" name="startVertical" id="0x7f0901c7" />
    <public type="id" name="staticLayout" id="0x7f0901c8" />
    <public type="id" name="staticPostLayout" id="0x7f0901c9" />
    <public type="id" name="stop" id="0x7f0901ca" />
    <public type="id" name="stretch" id="0x7f0901cb" />
    <public type="id" name="submenuarrow" id="0x7f0901cc" />
    <public type="id" name="submit_area" id="0x7f0901cd" />
    <public type="id" name="supportScrollUp" id="0x7f0901ce" />
    <public type="id" name="tabMode" id="0x7f0901cf" />
    <public type="id" name="tag_accessibility_actions" id="0x7f0901d0" />
    <public type="id" name="tag_accessibility_clickable_spans" id="0x7f0901d1" />
    <public type="id" name="tag_accessibility_heading" id="0x7f0901d2" />
    <public type="id" name="tag_accessibility_pane_title" id="0x7f0901d3" />
    <public type="id" name="tag_on_apply_window_listener" id="0x7f0901d4" />
    <public type="id" name="tag_on_receive_content_listener" id="0x7f0901d5" />
    <public type="id" name="tag_on_receive_content_mime_types" id="0x7f0901d6" />
    <public type="id" name="tag_screen_reader_focusable" id="0x7f0901d7" />
    <public type="id" name="tag_state_description" id="0x7f0901d8" />
    <public type="id" name="tag_transition_group" id="0x7f0901d9" />
    <public type="id" name="tag_unhandled_key_event_manager" id="0x7f0901da" />
    <public type="id" name="tag_unhandled_key_listeners" id="0x7f0901db" />
    <public type="id" name="tag_window_insets_animation_callback" id="0x7f0901dc" />
    <public type="id" name="text" id="0x7f0901dd" />
    <public type="id" name="text2" id="0x7f0901de" />
    <public type="id" name="textEnd" id="0x7f0901df" />
    <public type="id" name="textSpacerNoButtons" id="0x7f0901e0" />
    <public type="id" name="textSpacerNoTitle" id="0x7f0901e1" />
    <public type="id" name="textStart" id="0x7f0901e2" />
    <public type="id" name="textTop" id="0x7f0901e3" />
    <public type="id" name="text_input_end_icon" id="0x7f0901e4" />
    <public type="id" name="text_input_error_icon" id="0x7f0901e5" />
    <public type="id" name="text_input_start_icon" id="0x7f0901e6" />
    <public type="id" name="textinput_counter" id="0x7f0901e7" />
    <public type="id" name="textinput_error" id="0x7f0901e8" />
    <public type="id" name="textinput_helper_text" id="0x7f0901e9" />
    <public type="id" name="textinput_placeholder" id="0x7f0901ea" />
    <public type="id" name="textinput_prefix_text" id="0x7f0901eb" />
    <public type="id" name="textinput_suffix_text" id="0x7f0901ec" />
    <public type="id" name="time" id="0x7f0901ed" />
    <public type="id" name="title" id="0x7f0901ee" />
    <public type="id" name="titleDividerNoCustom" id="0x7f0901ef" />
    <public type="id" name="title_template" id="0x7f0901f0" />
    <public type="id" name="toggle" id="0x7f0901f1" />
    <public type="id" name="top" id="0x7f0901f2" />
    <public type="id" name="topPanel" id="0x7f0901f3" />
    <public type="id" name="touch_outside" id="0x7f0901f4" />
    <public type="id" name="transitionToEnd" id="0x7f0901f5" />
    <public type="id" name="transitionToStart" id="0x7f0901f6" />
    <public type="id" name="transition_clip" id="0x7f0901f7" />
    <public type="id" name="transition_current_scene" id="0x7f0901f8" />
    <public type="id" name="transition_image_transform" id="0x7f0901f9" />
    <public type="id" name="transition_layout_save" id="0x7f0901fa" />
    <public type="id" name="transition_pause_alpha" id="0x7f0901fb" />
    <public type="id" name="transition_position" id="0x7f0901fc" />
    <public type="id" name="transition_scene_layoutid_cache" id="0x7f0901fd" />
    <public type="id" name="transition_transform" id="0x7f0901fe" />
    <public type="id" name="triangle" id="0x7f0901ff" />
    <public type="id" name="unchecked" id="0x7f090200" />
    <public type="id" name="uniform" id="0x7f090201" />
    <public type="id" name="unlabeled" id="0x7f090202" />
    <public type="id" name="up" id="0x7f090203" />
    <public type="id" name="useLogo" id="0x7f090204" />
    <public type="id" name="vertical_only" id="0x7f090205" />
    <public type="id" name="view_offset_helper" id="0x7f090206" />
    <public type="id" name="view_transition" id="0x7f090207" />
    <public type="id" name="view_tree_lifecycle_owner" id="0x7f090208" />
    <public type="id" name="view_tree_on_back_pressed_dispatcher_owner" id="0x7f090209" />
    <public type="id" name="view_tree_saved_state_registry_owner" id="0x7f09020a" />
    <public type="id" name="view_tree_view_model_store_owner" id="0x7f09020b" />
    <public type="id" name="visible" id="0x7f09020c" />
    <public type="id" name="visible_removing_fragment_view_tag" id="0x7f09020d" />
    <public type="id" name="webView" id="0x7f09020e" />
    <public type="id" name="west" id="0x7f09020f" />
    <public type="id" name="wide" id="0x7f090210" />
    <public type="id" name="withText" id="0x7f090211" />
    <public type="id" name="with_icon" id="0x7f090212" />
    <public type="id" name="withinBounds" id="0x7f090213" />
    <public type="id" name="wrap" id="0x7f090214" />
    <public type="id" name="wrap_content" id="0x7f090215" />
    <public type="id" name="wrap_content_constrained" id="0x7f090216" />
    <public type="id" name="wrapped_composition_tag" id="0x7f090217" />
    <public type="id" name="x_left" id="0x7f090218" />
    <public type="id" name="x_right" id="0x7f090219" />
    <public type="integer" name="abc_config_activityDefaultDur" id="0x7f0a0000" />
    <public type="integer" name="abc_config_activityShortDur" id="0x7f0a0001" />
    <public type="integer" name="app_bar_elevation_anim_duration" id="0x7f0a0002" />
    <public type="integer" name="bottom_sheet_slide_duration" id="0x7f0a0003" />
    <public type="integer" name="cancel_button_image_alpha" id="0x7f0a0004" />
    <public type="integer" name="config_tooltipAnimTime" id="0x7f0a0005" />
    <public type="integer" name="default_icon_animation_duration" id="0x7f0a0006" />
    <public type="integer" name="design_snackbar_text_max_lines" id="0x7f0a0007" />
    <public type="integer" name="design_tab_indicator_anim_duration_ms" id="0x7f0a0008" />
    <public type="integer" name="google_play_services_version" id="0x7f0a0009" />
    <public type="integer" name="hide_password_duration" id="0x7f0a000a" />
    <public type="integer" name="m3_badge_max_number" id="0x7f0a000b" />
    <public type="integer" name="m3_sys_motion_duration_medium3" id="0x7f0a001b" />
    <public type="integer" name="m3_sys_motion_duration_short1" id="0x7f0a001d" />
    <public type="integer" name="m3_sys_motion_duration_short3" id="0x7f0a001f" />
    <public type="integer" name="material_motion_duration_long_1" id="0x7f0a0028" />
    <public type="integer" name="material_motion_duration_long_2" id="0x7f0a0029" />
    <public type="integer" name="material_motion_duration_medium_1" id="0x7f0a002a" />
    <public type="integer" name="material_motion_duration_medium_2" id="0x7f0a002b" />
    <public type="integer" name="material_motion_duration_short_1" id="0x7f0a002c" />
    <public type="integer" name="material_motion_duration_short_2" id="0x7f0a002d" />
    <public type="integer" name="material_motion_path" id="0x7f0a002e" />
    <public type="integer" name="mtrl_btn_anim_delay_ms" id="0x7f0a0030" />
    <public type="integer" name="mtrl_btn_anim_duration_ms" id="0x7f0a0031" />
    <public type="integer" name="mtrl_calendar_header_orientation" id="0x7f0a0032" />
    <public type="integer" name="mtrl_calendar_selection_text_lines" id="0x7f0a0033" />
    <public type="integer" name="mtrl_calendar_year_selector_span" id="0x7f0a0034" />
    <public type="integer" name="mtrl_card_anim_delay_ms" id="0x7f0a0035" />
    <public type="integer" name="mtrl_card_anim_duration_ms" id="0x7f0a0036" />
    <public type="integer" name="mtrl_chip_anim_duration" id="0x7f0a0037" />
    <public type="integer" name="mtrl_tab_indicator_anim_duration_ms" id="0x7f0a0040" />
    <public type="integer" name="mtrl_view_invisible" id="0x7f0a0042" />
    <public type="integer" name="show_password_duration" id="0x7f0a0044" />
    <public type="integer" name="status_bar_notification_info_maxnum" id="0x7f0a0045" />
    <public type="interpolator" name="btn_checkbox_checked_mtrl_animation_interpolator_0" id="0x7f0b0000" />
    <public type="interpolator" name="btn_checkbox_checked_mtrl_animation_interpolator_1" id="0x7f0b0001" />
    <public type="interpolator" name="btn_checkbox_unchecked_mtrl_animation_interpolator_0" id="0x7f0b0002" />
    <public type="interpolator" name="btn_checkbox_unchecked_mtrl_animation_interpolator_1" id="0x7f0b0003" />
    <public type="interpolator" name="btn_radio_to_off_mtrl_animation_interpolator_0" id="0x7f0b0004" />
    <public type="interpolator" name="btn_radio_to_on_mtrl_animation_interpolator_0" id="0x7f0b0005" />
    <public type="interpolator" name="fast_out_slow_in" id="0x7f0b0006" />
    <public type="interpolator" name="mtrl_fast_out_linear_in" id="0x7f0b000e" />
    <public type="interpolator" name="mtrl_fast_out_slow_in" id="0x7f0b000f" />
    <public type="interpolator" name="mtrl_linear" id="0x7f0b0010" />
    <public type="interpolator" name="mtrl_linear_out_slow_in" id="0x7f0b0011" />
    <public type="layout" name="abc_action_bar_title_item" id="0x7f0c0000" />
    <public type="layout" name="abc_action_bar_up_container" id="0x7f0c0001" />
    <public type="layout" name="abc_action_menu_item_layout" id="0x7f0c0002" />
    <public type="layout" name="abc_action_menu_layout" id="0x7f0c0003" />
    <public type="layout" name="abc_action_mode_bar" id="0x7f0c0004" />
    <public type="layout" name="abc_action_mode_close_item_material" id="0x7f0c0005" />
    <public type="layout" name="abc_activity_chooser_view" id="0x7f0c0006" />
    <public type="layout" name="abc_activity_chooser_view_list_item" id="0x7f0c0007" />
    <public type="layout" name="abc_alert_dialog_button_bar_material" id="0x7f0c0008" />
    <public type="layout" name="abc_alert_dialog_material" id="0x7f0c0009" />
    <public type="layout" name="abc_alert_dialog_title_material" id="0x7f0c000a" />
    <public type="layout" name="abc_cascading_menu_item_layout" id="0x7f0c000b" />
    <public type="layout" name="abc_dialog_title_material" id="0x7f0c000c" />
    <public type="layout" name="abc_expanded_menu_layout" id="0x7f0c000d" />
    <public type="layout" name="abc_list_menu_item_checkbox" id="0x7f0c000e" />
    <public type="layout" name="abc_list_menu_item_icon" id="0x7f0c000f" />
    <public type="layout" name="abc_list_menu_item_layout" id="0x7f0c0010" />
    <public type="layout" name="abc_list_menu_item_radio" id="0x7f0c0011" />
    <public type="layout" name="abc_popup_menu_header_item_layout" id="0x7f0c0012" />
    <public type="layout" name="abc_popup_menu_item_layout" id="0x7f0c0013" />
    <public type="layout" name="abc_screen_content_include" id="0x7f0c0014" />
    <public type="layout" name="abc_screen_simple" id="0x7f0c0015" />
    <public type="layout" name="abc_screen_simple_overlay_action_mode" id="0x7f0c0016" />
    <public type="layout" name="abc_screen_toolbar" id="0x7f0c0017" />
    <public type="layout" name="abc_search_dropdown_item_icons_2line" id="0x7f0c0018" />
    <public type="layout" name="abc_search_view" id="0x7f0c0019" />
    <public type="layout" name="abc_select_dialog_material" id="0x7f0c001a" />
    <public type="layout" name="abc_tooltip" id="0x7f0c001b" />
    <public type="layout" name="browser_actions_context_menu_page" id="0x7f0c001c" />
    <public type="layout" name="browser_actions_context_menu_row" id="0x7f0c001d" />
    <public type="layout" name="custom_dialog" id="0x7f0c001e" />
    <public type="layout" name="design_bottom_navigation_item" id="0x7f0c001f" />
    <public type="layout" name="design_menu_item_action_area" id="0x7f0c0025" />
    <public type="layout" name="design_navigation_menu_item" id="0x7f0c002b" />
    <public type="layout" name="design_text_input_end_icon" id="0x7f0c002c" />
    <public type="layout" name="design_text_input_start_icon" id="0x7f0c002d" />
    <public type="layout" name="externalurls_webview" id="0x7f0c002e" />
    <public type="layout" name="ime_base_split_test_activity" id="0x7f0c002f" />
    <public type="layout" name="ime_secondary_split_test_activity" id="0x7f0c0030" />
    <public type="layout" name="material_clock_display" id="0x7f0c0037" />
    <public type="layout" name="material_clock_display_divider" id="0x7f0c0038" />
    <public type="layout" name="material_clock_period_toggle" id="0x7f0c0039" />
    <public type="layout" name="material_clock_period_toggle_land" id="0x7f0c003a" />
    <public type="layout" name="material_clockface_textview" id="0x7f0c003b" />
    <public type="layout" name="material_clockface_view" id="0x7f0c003c" />
    <public type="layout" name="material_radial_view_group" id="0x7f0c003d" />
    <public type="layout" name="material_time_chip" id="0x7f0c003f" />
    <public type="layout" name="material_time_input" id="0x7f0c0040" />
    <public type="layout" name="material_timepicker" id="0x7f0c0041" />
    <public type="layout" name="mtrl_alert_dialog" id="0x7f0c0044" />
    <public type="layout" name="mtrl_alert_dialog_actions" id="0x7f0c0045" />
    <public type="layout" name="mtrl_alert_dialog_title" id="0x7f0c0046" />
    <public type="layout" name="mtrl_alert_select_dialog_item" id="0x7f0c0047" />
    <public type="layout" name="mtrl_alert_select_dialog_multichoice" id="0x7f0c0048" />
    <public type="layout" name="mtrl_alert_select_dialog_singlechoice" id="0x7f0c0049" />
    <public type="layout" name="mtrl_auto_complete_simple_item" id="0x7f0c004a" />
    <public type="layout" name="mtrl_calendar_day" id="0x7f0c004b" />
    <public type="layout" name="mtrl_calendar_day_of_week" id="0x7f0c004c" />
    <public type="layout" name="mtrl_calendar_days_of_week" id="0x7f0c004d" />
    <public type="layout" name="mtrl_calendar_horizontal" id="0x7f0c004e" />
    <public type="layout" name="mtrl_calendar_month" id="0x7f0c004f" />
    <public type="layout" name="mtrl_calendar_month_labeled" id="0x7f0c0050" />
    <public type="layout" name="mtrl_calendar_month_navigation" id="0x7f0c0051" />
    <public type="layout" name="mtrl_calendar_months" id="0x7f0c0052" />
    <public type="layout" name="mtrl_calendar_vertical" id="0x7f0c0053" />
    <public type="layout" name="mtrl_calendar_year" id="0x7f0c0054" />
    <public type="layout" name="mtrl_picker_actions" id="0x7f0c0058" />
    <public type="layout" name="mtrl_picker_dialog" id="0x7f0c0059" />
    <public type="layout" name="mtrl_picker_fullscreen" id="0x7f0c005a" />
    <public type="layout" name="mtrl_picker_header_dialog" id="0x7f0c005b" />
    <public type="layout" name="mtrl_picker_header_fullscreen" id="0x7f0c005c" />
    <public type="layout" name="mtrl_picker_header_selection_text" id="0x7f0c005d" />
    <public type="layout" name="mtrl_picker_header_title_text" id="0x7f0c005e" />
    <public type="layout" name="mtrl_picker_header_toggle" id="0x7f0c005f" />
    <public type="layout" name="notification_action" id="0x7f0c0064" />
    <public type="layout" name="notification_action_tombstone" id="0x7f0c0065" />
    <public type="layout" name="notification_template_custom_big" id="0x7f0c0066" />
    <public type="layout" name="notification_template_icon_group" id="0x7f0c0067" />
    <public type="layout" name="notification_template_part_chronometer" id="0x7f0c0068" />
    <public type="layout" name="notification_template_part_time" id="0x7f0c0069" />
    <public type="layout" name="onesignal_bgimage_notif_layout" id="0x7f0c006a" />
    <public type="layout" name="select_dialog_item_material" id="0x7f0c006b" />
    <public type="layout" name="select_dialog_multichoice_material" id="0x7f0c006c" />
    <public type="layout" name="select_dialog_singlechoice_material" id="0x7f0c006d" />
    <public type="layout" name="support_simple_spinner_dropdown_item" id="0x7f0c006f" />
    <public type="layout" name="web_view" id="0x7f0c0070" />
    <public type="mipmap" name="ic_launcher" id="0x7f0e0001" />
    <public type="mipmap" name="ic_launcher_round" id="0x7f0e0002" />
    <public type="plurals" name="mtrl_badge_content_description" id="0x7f0f0000" />
    <public type="raw" name="loader_animation" id="0x7f100002" />
    <public type="raw" name="sample" id="0x7f100003" />
    <public type="string" name="abc_action_bar_home_description" id="0x7f110000" />
    <public type="string" name="abc_action_bar_up_description" id="0x7f110001" />
    <public type="string" name="abc_action_menu_overflow_description" id="0x7f110002" />
    <public type="string" name="abc_action_mode_done" id="0x7f110003" />
    <public type="string" name="abc_activity_chooser_view_see_all" id="0x7f110004" />
    <public type="string" name="abc_activitychooserview_choose_application" id="0x7f110005" />
    <public type="string" name="abc_capital_off" id="0x7f110006" />
    <public type="string" name="abc_capital_on" id="0x7f110007" />
    <public type="string" name="abc_menu_alt_shortcut_label" id="0x7f110008" />
    <public type="string" name="abc_menu_ctrl_shortcut_label" id="0x7f110009" />
    <public type="string" name="abc_menu_delete_shortcut_label" id="0x7f11000a" />
    <public type="string" name="abc_menu_enter_shortcut_label" id="0x7f11000b" />
    <public type="string" name="abc_menu_function_shortcut_label" id="0x7f11000c" />
    <public type="string" name="abc_menu_meta_shortcut_label" id="0x7f11000d" />
    <public type="string" name="abc_menu_shift_shortcut_label" id="0x7f11000e" />
    <public type="string" name="abc_menu_space_shortcut_label" id="0x7f11000f" />
    <public type="string" name="abc_menu_sym_shortcut_label" id="0x7f110010" />
    <public type="string" name="abc_prepend_shortcut_label" id="0x7f110011" />
    <public type="string" name="abc_search_hint" id="0x7f110012" />
    <public type="string" name="abc_searchview_description_clear" id="0x7f110013" />
    <public type="string" name="abc_searchview_description_query" id="0x7f110014" />
    <public type="string" name="abc_searchview_description_search" id="0x7f110015" />
    <public type="string" name="abc_searchview_description_submit" id="0x7f110016" />
    <public type="string" name="abc_searchview_description_voice" id="0x7f110017" />
    <public type="string" name="abc_shareactionprovider_share_with" id="0x7f110018" />
    <public type="string" name="abc_shareactionprovider_share_with_application" id="0x7f110019" />
    <public type="string" name="abc_toolbar_collapse_description" id="0x7f11001a" />
    <public type="string" name="androidx_startup" id="0x7f11001b" />
    <public type="string" name="app_name" id="0x7f11001c" />
    <public type="string" name="appbar_scrolling_view_behavior" id="0x7f11001d" />
    <public type="string" name="bottom_sheet_behavior" id="0x7f11001e" />
    <public type="string" name="bottomsheet_action_collapse" id="0x7f11001f" />
    <public type="string" name="bottomsheet_action_expand" id="0x7f110020" />
    <public type="string" name="bottomsheet_action_expand_halfway" id="0x7f110021" />
    <public type="string" name="bottomsheet_drag_handle_clicked" id="0x7f110022" />
    <public type="string" name="bottomsheet_drag_handle_content_description" id="0x7f110023" />
    <public type="string" name="call_notification_answer_action" id="0x7f110024" />
    <public type="string" name="call_notification_answer_video_action" id="0x7f110025" />
    <public type="string" name="call_notification_decline_action" id="0x7f110026" />
    <public type="string" name="call_notification_hang_up_action" id="0x7f110027" />
    <public type="string" name="call_notification_incoming_text" id="0x7f110028" />
    <public type="string" name="call_notification_ongoing_text" id="0x7f110029" />
    <public type="string" name="call_notification_screening_text" id="0x7f11002a" />
    <public type="string" name="character_counter_content_description" id="0x7f11002b" />
    <public type="string" name="character_counter_overflowed_content_description" id="0x7f11002c" />
    <public type="string" name="character_counter_pattern" id="0x7f11002d" />
    <public type="string" name="clear_text_end_icon_content_description" id="0x7f11002e" />
    <public type="string" name="close_drawer" id="0x7f11002f" />
    <public type="string" name="close_sheet" id="0x7f110030" />
    <public type="string" name="common_google_play_services_enable_button" id="0x7f110031" />
    <public type="string" name="common_google_play_services_enable_text" id="0x7f110032" />
    <public type="string" name="common_google_play_services_enable_title" id="0x7f110033" />
    <public type="string" name="common_google_play_services_install_button" id="0x7f110034" />
    <public type="string" name="common_google_play_services_install_text" id="0x7f110035" />
    <public type="string" name="common_google_play_services_install_title" id="0x7f110036" />
    <public type="string" name="common_google_play_services_notification_channel_name" id="0x7f110037" />
    <public type="string" name="common_google_play_services_notification_ticker" id="0x7f110038" />
    <public type="string" name="common_google_play_services_unknown_issue" id="0x7f110039" />
    <public type="string" name="common_google_play_services_unsupported_text" id="0x7f11003a" />
    <public type="string" name="common_google_play_services_update_button" id="0x7f11003b" />
    <public type="string" name="common_google_play_services_update_text" id="0x7f11003c" />
    <public type="string" name="common_google_play_services_update_title" id="0x7f11003d" />
    <public type="string" name="common_google_play_services_updating_text" id="0x7f11003e" />
    <public type="string" name="common_google_play_services_wear_update_text" id="0x7f11003f" />
    <public type="string" name="common_open_on_phone" id="0x7f110040" />
    <public type="string" name="common_signin_button_text" id="0x7f110041" />
    <public type="string" name="common_signin_button_text_long" id="0x7f110042" />
    <public type="string" name="copy_toast_msg" id="0x7f110043" />
    <public type="string" name="default_error_message" id="0x7f110044" />
    <public type="string" name="default_popup_window_title" id="0x7f110045" />
    <public type="string" name="default_web_client_id" id="0x7f110046" />
    <public type="string" name="dropdown_menu" id="0x7f11004a" />
    <public type="string" name="error_a11y_label" id="0x7f11004b" />
    <public type="string" name="error_icon_content_description" id="0x7f11004c" />
    <public type="string" name="exposed_dropdown_menu_content_description" id="0x7f11004d" />
    <public type="string" name="fab_transformation_scrim_behavior" id="0x7f11004e" />
    <public type="string" name="fab_transformation_sheet_behavior" id="0x7f11004f" />
    <public type="string" name="facebook_app_id" id="0x7f110050" />
    <public type="string" name="facebook_client_token" id="0x7f110051" />
    <public type="string" name="fcm_fallback_notification_channel_label" id="0x7f110056" />
    <public type="string" name="firebase_database_url" id="0x7f110057" />
    <public type="string" name="fontawesome_version" id="0x7f110058" />
    <public type="string" name="gcm_defaultSenderId" id="0x7f110059" />
    <public type="string" name="google_api_key" id="0x7f11005b" />
    <public type="string" name="google_app_id" id="0x7f11005c" />
    <public type="string" name="google_crash_reporting_api_key" id="0x7f11005d" />
    <public type="string" name="google_storage_bucket" id="0x7f11005e" />
    <public type="string" name="icon_content_description" id="0x7f110060" />
    <public type="string" name="iconics_lib_version" id="0x7f110061" />
    <public type="string" name="iconics_typeface_api_version" id="0x7f110062" />
    <public type="string" name="in_progress" id="0x7f110063" />
    <public type="string" name="indeterminate" id="0x7f110064" />
    <public type="string" name="item_view_role_description" id="0x7f110065" />
    <public type="string" name="location_permission_missing_message" id="0x7f11007b" />
    <public type="string" name="location_permission_missing_title" id="0x7f11007c" />
    <public type="string" name="location_permission_name_for_title" id="0x7f11007d" />
    <public type="string" name="location_permission_settings_message" id="0x7f11007e" />
    <public type="string" name="m3_exceed_max_badge_text_suffix" id="0x7f11007f" />
    <public type="string" name="m3_ref_typeface_brand_regular" id="0x7f110081" />
    <public type="string" name="m3_ref_typeface_plain_medium" id="0x7f110082" />
    <public type="string" name="m3_ref_typeface_plain_regular" id="0x7f110083" />
    <public type="string" name="m3c_bottom_sheet_collapse_description" id="0x7f11008f" />
    <public type="string" name="m3c_bottom_sheet_dismiss_description" id="0x7f110090" />
    <public type="string" name="m3c_bottom_sheet_drag_handle_description" id="0x7f110091" />
    <public type="string" name="m3c_bottom_sheet_expand_description" id="0x7f110092" />
    <public type="string" name="m3c_bottom_sheet_pane_title" id="0x7f110093" />
    <public type="string" name="material_clock_display_divider" id="0x7f1100c7" />
    <public type="string" name="material_clock_toggle_content_description" id="0x7f1100c8" />
    <public type="string" name="material_motion_easing_accelerated" id="0x7f1100ce" />
    <public type="string" name="material_motion_easing_decelerated" id="0x7f1100cf" />
    <public type="string" name="material_motion_easing_emphasized" id="0x7f1100d0" />
    <public type="string" name="material_motion_easing_linear" id="0x7f1100d1" />
    <public type="string" name="material_motion_easing_standard" id="0x7f1100d2" />
    <public type="string" name="material_timepicker_am" id="0x7f1100d6" />
    <public type="string" name="material_timepicker_pm" id="0x7f1100da" />
    <public type="string" name="mtrl_badge_numberless_content_description" id="0x7f1100dd" />
    <public type="string" name="mtrl_checkbox_button_icon_path_checked" id="0x7f1100de" />
    <public type="string" name="mtrl_checkbox_button_icon_path_group_name" id="0x7f1100df" />
    <public type="string" name="mtrl_checkbox_button_icon_path_indeterminate" id="0x7f1100e0" />
    <public type="string" name="mtrl_checkbox_button_icon_path_name" id="0x7f1100e1" />
    <public type="string" name="mtrl_checkbox_button_path_checked" id="0x7f1100e2" />
    <public type="string" name="mtrl_checkbox_button_path_group_name" id="0x7f1100e3" />
    <public type="string" name="mtrl_checkbox_button_path_name" id="0x7f1100e4" />
    <public type="string" name="mtrl_checkbox_button_path_unchecked" id="0x7f1100e5" />
    <public type="string" name="mtrl_checkbox_state_description_checked" id="0x7f1100e6" />
    <public type="string" name="mtrl_checkbox_state_description_indeterminate" id="0x7f1100e7" />
    <public type="string" name="mtrl_checkbox_state_description_unchecked" id="0x7f1100e8" />
    <public type="string" name="mtrl_chip_close_icon_content_description" id="0x7f1100e9" />
    <public type="string" name="mtrl_exceed_max_badge_number_content_description" id="0x7f1100ea" />
    <public type="string" name="mtrl_exceed_max_badge_number_suffix" id="0x7f1100eb" />
    <public type="string" name="mtrl_picker_a11y_next_month" id="0x7f1100ec" />
    <public type="string" name="mtrl_picker_a11y_prev_month" id="0x7f1100ed" />
    <public type="string" name="mtrl_picker_cancel" id="0x7f1100f1" />
    <public type="string" name="mtrl_picker_confirm" id="0x7f1100f2" />
    <public type="string" name="mtrl_picker_day_of_week_column_header" id="0x7f1100f6" />
    <public type="string" name="mtrl_picker_navigate_to_current_year_description" id="0x7f1100fc" />
    <public type="string" name="mtrl_picker_navigate_to_year_description" id="0x7f1100fd" />
    <public type="string" name="mtrl_picker_save" id="0x7f110104" />
    <public type="string" name="mtrl_picker_toggle_to_calendar_input_mode" id="0x7f11010d" />
    <public type="string" name="mtrl_picker_toggle_to_day_selection" id="0x7f11010e" />
    <public type="string" name="mtrl_picker_toggle_to_text_input_mode" id="0x7f11010f" />
    <public type="string" name="mtrl_picker_toggle_to_year_selection" id="0x7f110110" />
    <public type="string" name="navigation_menu" id="0x7f11011b" />
    <public type="string" name="not_selected" id="0x7f11011c" />
    <public type="string" name="notification_permission_name_for_title" id="0x7f11011d" />
    <public type="string" name="notification_permission_settings_message" id="0x7f11011e" />
    <public type="string" name="orufy_connect_client_id" id="0x7f11011f" />
    <public type="string" name="password_toggle_content_description" id="0x7f110120" />
    <public type="string" name="path_password_eye" id="0x7f110121" />
    <public type="string" name="path_password_eye_mask_strike_through" id="0x7f110122" />
    <public type="string" name="path_password_eye_mask_visible" id="0x7f110123" />
    <public type="string" name="path_password_strike_through" id="0x7f110124" />
    <public type="string" name="permission_not_available_message" id="0x7f110125" />
    <public type="string" name="permission_not_available_open_settings_option" id="0x7f110126" />
    <public type="string" name="permission_not_available_title" id="0x7f110127" />
    <public type="string" name="project_id" id="0x7f110128" />
    <public type="string" name="range_end" id="0x7f110129" />
    <public type="string" name="range_start" id="0x7f11012a" />
    <public type="string" name="received_message" id="0x7f11012b" />
    <public type="string" name="search_menu_title" id="0x7f11012c" />
    <public type="string" name="searchbar_scrolling_view_behavior" id="0x7f11012d" />
    <public type="string" name="searchview_clear_text_content_description" id="0x7f11012e" />
    <public type="string" name="searchview_navigation_content_description" id="0x7f11012f" />
    <public type="string" name="selected" id="0x7f110130" />
    <public type="string" name="send_otp" id="0x7f110131" />
    <public type="string" name="side_sheet_accessibility_pane_title" id="0x7f110132" />
    <public type="string" name="state_off" id="0x7f110134" />
    <public type="string" name="state_on" id="0x7f110135" />
    <public type="string" name="status_bar_notification_info_overflow" id="0x7f110136" />
    <public type="string" name="switch_role" id="0x7f110137" />
    <public type="string" name="tab" id="0x7f110138" />
    <public type="string" name="template_percent" id="0x7f110139" />
    <public type="string" name="title_activity_hello" id="0x7f11013a" />
    <public type="string" name="tooltip_description" id="0x7f11013b" />
    <public type="string" name="tooltip_label" id="0x7f11013c" />
    <public type="style" name="AlertDialog.AppCompat" id="0x7f120000" />
    <public type="style" name="AlertDialog.AppCompat.Light" id="0x7f120001" />
    <public type="style" name="Animation.AppCompat.Dialog" id="0x7f120002" />
    <public type="style" name="Animation.AppCompat.DropDownUp" id="0x7f120003" />
    <public type="style" name="Animation.AppCompat.Tooltip" id="0x7f120004" />
    <public type="style" name="Animation.MaterialComponents.BottomSheetDialog" id="0x7f12000a" />
    <public type="style" name="AppTheme" id="0x7f12000b" />
    <public type="style" name="Base.AlertDialog.AppCompat" id="0x7f12000c" />
    <public type="style" name="Base.AlertDialog.AppCompat.Light" id="0x7f12000d" />
    <public type="style" name="Base.Animation.AppCompat.Dialog" id="0x7f12000e" />
    <public type="style" name="Base.Animation.AppCompat.DropDownUp" id="0x7f12000f" />
    <public type="style" name="Base.Animation.AppCompat.Tooltip" id="0x7f120010" />
    <public type="style" name="Base.CardView" id="0x7f120011" />
    <public type="style" name="Base.DialogWindowTitle.AppCompat" id="0x7f120012" />
    <public type="style" name="Base.DialogWindowTitleBackground.AppCompat" id="0x7f120013" />
    <public type="style" name="Base.MaterialAlertDialog.MaterialComponents.Title.Icon" id="0x7f120014" />
    <public type="style" name="Base.MaterialAlertDialog.MaterialComponents.Title.Panel" id="0x7f120015" />
    <public type="style" name="Base.MaterialAlertDialog.MaterialComponents.Title.Text" id="0x7f120016" />
    <public type="style" name="Base.TextAppearance.AppCompat" id="0x7f120017" />
    <public type="style" name="Base.TextAppearance.AppCompat.Body1" id="0x7f120018" />
    <public type="style" name="Base.TextAppearance.AppCompat.Body2" id="0x7f120019" />
    <public type="style" name="Base.TextAppearance.AppCompat.Button" id="0x7f12001a" />
    <public type="style" name="Base.TextAppearance.AppCompat.Caption" id="0x7f12001b" />
    <public type="style" name="Base.TextAppearance.AppCompat.Display1" id="0x7f12001c" />
    <public type="style" name="Base.TextAppearance.AppCompat.Display2" id="0x7f12001d" />
    <public type="style" name="Base.TextAppearance.AppCompat.Display3" id="0x7f12001e" />
    <public type="style" name="Base.TextAppearance.AppCompat.Display4" id="0x7f12001f" />
    <public type="style" name="Base.TextAppearance.AppCompat.Headline" id="0x7f120020" />
    <public type="style" name="Base.TextAppearance.AppCompat.Inverse" id="0x7f120021" />
    <public type="style" name="Base.TextAppearance.AppCompat.Large" id="0x7f120022" />
    <public type="style" name="Base.TextAppearance.AppCompat.Large.Inverse" id="0x7f120023" />
    <public type="style" name="Base.TextAppearance.AppCompat.Medium" id="0x7f120026" />
    <public type="style" name="Base.TextAppearance.AppCompat.Medium.Inverse" id="0x7f120027" />
    <public type="style" name="Base.TextAppearance.AppCompat.Menu" id="0x7f120028" />
    <public type="style" name="Base.TextAppearance.AppCompat.SearchResult.Subtitle" id="0x7f12002a" />
    <public type="style" name="Base.TextAppearance.AppCompat.SearchResult.Title" id="0x7f12002b" />
    <public type="style" name="Base.TextAppearance.AppCompat.Small" id="0x7f12002c" />
    <public type="style" name="Base.TextAppearance.AppCompat.Small.Inverse" id="0x7f12002d" />
    <public type="style" name="Base.TextAppearance.AppCompat.Subhead" id="0x7f12002e" />
    <public type="style" name="Base.TextAppearance.AppCompat.Subhead.Inverse" id="0x7f12002f" />
    <public type="style" name="Base.TextAppearance.AppCompat.Title" id="0x7f120030" />
    <public type="style" name="Base.TextAppearance.AppCompat.Title.Inverse" id="0x7f120031" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionBar.Menu" id="0x7f120033" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle" id="0x7f120034" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" id="0x7f120035" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title" id="0x7f120036" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" id="0x7f120037" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle" id="0x7f120038" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionMode.Title" id="0x7f120039" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.Button" id="0x7f12003a" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored" id="0x7f12003b" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.Button.Colored" id="0x7f12003c" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.Button.Inverse" id="0x7f12003d" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.DropDownItem" id="0x7f12003e" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Header" id="0x7f12003f" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Large" id="0x7f120040" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Small" id="0x7f120041" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.Switch" id="0x7f120042" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem" id="0x7f120043" />
    <public type="style" name="Base.TextAppearance.Material3.Search" id="0x7f120044" />
    <public type="style" name="Base.TextAppearance.MaterialComponents.Badge" id="0x7f120045" />
    <public type="style" name="Base.TextAppearance.MaterialComponents.Button" id="0x7f120046" />
    <public type="style" name="Base.TextAppearance.MaterialComponents.Headline6" id="0x7f120047" />
    <public type="style" name="Base.TextAppearance.MaterialComponents.Subtitle2" id="0x7f120048" />
    <public type="style" name="Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item" id="0x7f120049" />
    <public type="style" name="Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle" id="0x7f12004a" />
    <public type="style" name="Base.TextAppearance.Widget.AppCompat.Toolbar.Title" id="0x7f12004b" />
    <public type="style" name="Base.Theme.AppCompat" id="0x7f12004c" />
    <public type="style" name="Base.Theme.AppCompat.CompactMenu" id="0x7f12004d" />
    <public type="style" name="Base.Theme.AppCompat.Dialog" id="0x7f12004e" />
    <public type="style" name="Base.Theme.AppCompat.Dialog.Alert" id="0x7f12004f" />
    <public type="style" name="Base.Theme.AppCompat.Dialog.FixedSize" id="0x7f120050" />
    <public type="style" name="Base.Theme.AppCompat.Dialog.MinWidth" id="0x7f120051" />
    <public type="style" name="Base.Theme.AppCompat.DialogWhenLarge" id="0x7f120052" />
    <public type="style" name="Base.Theme.AppCompat.Light" id="0x7f120053" />
    <public type="style" name="Base.Theme.AppCompat.Light.DarkActionBar" id="0x7f120054" />
    <public type="style" name="Base.Theme.AppCompat.Light.Dialog" id="0x7f120055" />
    <public type="style" name="Base.Theme.AppCompat.Light.Dialog.Alert" id="0x7f120056" />
    <public type="style" name="Base.Theme.AppCompat.Light.Dialog.FixedSize" id="0x7f120057" />
    <public type="style" name="Base.Theme.AppCompat.Light.Dialog.MinWidth" id="0x7f120058" />
    <public type="style" name="Base.Theme.AppCompat.Light.DialogWhenLarge" id="0x7f120059" />
    <public type="style" name="Base.Theme.MaterialComponents" id="0x7f120066" />
    <public type="style" name="Base.Theme.MaterialComponents.Bridge" id="0x7f120067" />
    <public type="style" name="Base.Theme.MaterialComponents.CompactMenu" id="0x7f120068" />
    <public type="style" name="Base.Theme.MaterialComponents.Dialog" id="0x7f120069" />
    <public type="style" name="Base.Theme.MaterialComponents.Dialog.Alert" id="0x7f12006a" />
    <public type="style" name="Base.Theme.MaterialComponents.Dialog.Bridge" id="0x7f12006b" />
    <public type="style" name="Base.Theme.MaterialComponents.Dialog.FixedSize" id="0x7f12006c" />
    <public type="style" name="Base.Theme.MaterialComponents.Dialog.MinWidth" id="0x7f12006d" />
    <public type="style" name="Base.Theme.MaterialComponents.DialogWhenLarge" id="0x7f12006e" />
    <public type="style" name="Base.Theme.MaterialComponents.Light" id="0x7f12006f" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.Bridge" id="0x7f120070" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.DarkActionBar" id="0x7f120071" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.DarkActionBar.Bridge" id="0x7f120072" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.Dialog" id="0x7f120073" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.Dialog.Alert" id="0x7f120074" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.Dialog.Bridge" id="0x7f120075" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.Dialog.FixedSize" id="0x7f120076" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.Dialog.MinWidth" id="0x7f120077" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.DialogWhenLarge" id="0x7f120078" />
    <public type="style" name="Base.Theme.SplashScreen" id="0x7f120079" />
    <public type="style" name="Base.Theme.SplashScreen.DayNight" id="0x7f12007a" />
    <public type="style" name="Base.Theme.SplashScreen.Light" id="0x7f12007b" />
    <public type="style" name="Base.ThemeOverlay.AppCompat" id="0x7f12007c" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.ActionBar" id="0x7f12007d" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.Dark" id="0x7f12007e" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.Dark.ActionBar" id="0x7f12007f" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.Dialog" id="0x7f120080" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.Dialog.Alert" id="0x7f120081" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.Light" id="0x7f120082" />
    <public type="style" name="Base.ThemeOverlay.MaterialComponents.Dialog" id="0x7f120088" />
    <public type="style" name="Base.ThemeOverlay.MaterialComponents.Dialog.Alert" id="0x7f120089" />
    <public type="style" name="Base.ThemeOverlay.MaterialComponents.Dialog.Alert.Framework" id="0x7f12008a" />
    <public type="style" name="Base.ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework" id="0x7f12008b" />
    <public type="style" name="Base.ThemeOverlay.MaterialComponents.MaterialAlertDialog" id="0x7f12008c" />
    <public type="style" name="Base.V14.Theme.MaterialComponents" id="0x7f120095" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Bridge" id="0x7f120096" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Dialog" id="0x7f120097" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Dialog.Bridge" id="0x7f120098" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Light" id="0x7f120099" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Light.Bridge" id="0x7f12009a" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Light.DarkActionBar.Bridge" id="0x7f12009b" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Light.Dialog" id="0x7f12009c" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Light.Dialog.Bridge" id="0x7f12009d" />
    <public type="style" name="Base.V14.ThemeOverlay.MaterialComponents.BottomSheetDialog" id="0x7f1200a0" />
    <public type="style" name="Base.V14.ThemeOverlay.MaterialComponents.Dialog" id="0x7f1200a1" />
    <public type="style" name="Base.V14.ThemeOverlay.MaterialComponents.Dialog.Alert" id="0x7f1200a2" />
    <public type="style" name="Base.V14.ThemeOverlay.MaterialComponents.MaterialAlertDialog" id="0x7f1200a3" />
    <public type="style" name="Base.V14.Widget.MaterialComponents.AutoCompleteTextView" id="0x7f1200a4" />
    <public type="style" name="Base.V21.Theme.AppCompat" id="0x7f1200a5" />
    <public type="style" name="Base.V21.Theme.AppCompat.Dialog" id="0x7f1200a6" />
    <public type="style" name="Base.V21.Theme.AppCompat.Light" id="0x7f1200a7" />
    <public type="style" name="Base.V21.Theme.AppCompat.Light.Dialog" id="0x7f1200a8" />
    <public type="style" name="Base.V21.Theme.MaterialComponents" id="0x7f1200a9" />
    <public type="style" name="Base.V21.Theme.MaterialComponents.Dialog" id="0x7f1200aa" />
    <public type="style" name="Base.V21.Theme.MaterialComponents.Light" id="0x7f1200ab" />
    <public type="style" name="Base.V21.Theme.MaterialComponents.Light.Dialog" id="0x7f1200ac" />
    <public type="style" name="Base.V21.ThemeOverlay.AppCompat.Dialog" id="0x7f1200ad" />
    <public type="style" name="Base.V21.ThemeOverlay.MaterialComponents.BottomSheetDialog" id="0x7f1200b0" />
    <public type="style" name="Base.V22.Theme.AppCompat" id="0x7f1200b1" />
    <public type="style" name="Base.V22.Theme.AppCompat.Light" id="0x7f1200b2" />
    <public type="style" name="Base.V23.Theme.AppCompat" id="0x7f1200b3" />
    <public type="style" name="Base.V23.Theme.AppCompat.Light" id="0x7f1200b4" />
    <public type="style" name="Base.V26.Theme.AppCompat" id="0x7f1200b9" />
    <public type="style" name="Base.V26.Theme.AppCompat.Light" id="0x7f1200ba" />
    <public type="style" name="Base.V26.Widget.AppCompat.Toolbar" id="0x7f1200bb" />
    <public type="style" name="Base.V28.Theme.AppCompat" id="0x7f1200bc" />
    <public type="style" name="Base.V28.Theme.AppCompat.Light" id="0x7f1200bd" />
    <public type="style" name="Base.V7.Theme.AppCompat" id="0x7f1200be" />
    <public type="style" name="Base.V7.Theme.AppCompat.Dialog" id="0x7f1200bf" />
    <public type="style" name="Base.V7.Theme.AppCompat.Light" id="0x7f1200c0" />
    <public type="style" name="Base.V7.Theme.AppCompat.Light.Dialog" id="0x7f1200c1" />
    <public type="style" name="Base.V7.ThemeOverlay.AppCompat.Dialog" id="0x7f1200c2" />
    <public type="style" name="Base.V7.Widget.AppCompat.Toolbar" id="0x7f1200c5" />
    <public type="style" name="Base.Widget.AppCompat.ActionBar" id="0x7f1200c6" />
    <public type="style" name="Base.Widget.AppCompat.ActionBar.Solid" id="0x7f1200c7" />
    <public type="style" name="Base.Widget.AppCompat.ActionBar.TabBar" id="0x7f1200c8" />
    <public type="style" name="Base.Widget.AppCompat.ActionBar.TabText" id="0x7f1200c9" />
    <public type="style" name="Base.Widget.AppCompat.ActionBar.TabView" id="0x7f1200ca" />
    <public type="style" name="Base.Widget.AppCompat.ActionButton" id="0x7f1200cb" />
    <public type="style" name="Base.Widget.AppCompat.ActionButton.CloseMode" id="0x7f1200cc" />
    <public type="style" name="Base.Widget.AppCompat.ActionButton.Overflow" id="0x7f1200cd" />
    <public type="style" name="Base.Widget.AppCompat.ActionMode" id="0x7f1200ce" />
    <public type="style" name="Base.Widget.AppCompat.ActivityChooserView" id="0x7f1200cf" />
    <public type="style" name="Base.Widget.AppCompat.AutoCompleteTextView" id="0x7f1200d0" />
    <public type="style" name="Base.Widget.AppCompat.Button" id="0x7f1200d1" />
    <public type="style" name="Base.Widget.AppCompat.Button.Borderless" id="0x7f1200d2" />
    <public type="style" name="Base.Widget.AppCompat.Button.Borderless.Colored" id="0x7f1200d3" />
    <public type="style" name="Base.Widget.AppCompat.Button.ButtonBar.AlertDialog" id="0x7f1200d4" />
    <public type="style" name="Base.Widget.AppCompat.Button.Small" id="0x7f1200d6" />
    <public type="style" name="Base.Widget.AppCompat.ButtonBar" id="0x7f1200d7" />
    <public type="style" name="Base.Widget.AppCompat.ButtonBar.AlertDialog" id="0x7f1200d8" />
    <public type="style" name="Base.Widget.AppCompat.CompoundButton.CheckBox" id="0x7f1200d9" />
    <public type="style" name="Base.Widget.AppCompat.CompoundButton.RadioButton" id="0x7f1200da" />
    <public type="style" name="Base.Widget.AppCompat.CompoundButton.Switch" id="0x7f1200db" />
    <public type="style" name="Base.Widget.AppCompat.DrawerArrowToggle" id="0x7f1200dc" />
    <public type="style" name="Base.Widget.AppCompat.DrawerArrowToggle.Common" id="0x7f1200dd" />
    <public type="style" name="Base.Widget.AppCompat.DropDownItem.Spinner" id="0x7f1200de" />
    <public type="style" name="Base.Widget.AppCompat.EditText" id="0x7f1200df" />
    <public type="style" name="Base.Widget.AppCompat.ImageButton" id="0x7f1200e0" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar" id="0x7f1200e1" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar.Solid" id="0x7f1200e2" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar.TabBar" id="0x7f1200e3" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar.TabText" id="0x7f1200e4" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar.TabView" id="0x7f1200e6" />
    <public type="style" name="Base.Widget.AppCompat.Light.PopupMenu" id="0x7f1200e7" />
    <public type="style" name="Base.Widget.AppCompat.Light.PopupMenu.Overflow" id="0x7f1200e8" />
    <public type="style" name="Base.Widget.AppCompat.ListMenuView" id="0x7f1200e9" />
    <public type="style" name="Base.Widget.AppCompat.ListPopupWindow" id="0x7f1200ea" />
    <public type="style" name="Base.Widget.AppCompat.ListView" id="0x7f1200eb" />
    <public type="style" name="Base.Widget.AppCompat.ListView.DropDown" id="0x7f1200ec" />
    <public type="style" name="Base.Widget.AppCompat.ListView.Menu" id="0x7f1200ed" />
    <public type="style" name="Base.Widget.AppCompat.PopupMenu" id="0x7f1200ee" />
    <public type="style" name="Base.Widget.AppCompat.PopupMenu.Overflow" id="0x7f1200ef" />
    <public type="style" name="Base.Widget.AppCompat.RatingBar" id="0x7f1200f3" />
    <public type="style" name="Base.Widget.AppCompat.RatingBar.Indicator" id="0x7f1200f4" />
    <public type="style" name="Base.Widget.AppCompat.RatingBar.Small" id="0x7f1200f5" />
    <public type="style" name="Base.Widget.AppCompat.SearchView" id="0x7f1200f6" />
    <public type="style" name="Base.Widget.AppCompat.SearchView.ActionBar" id="0x7f1200f7" />
    <public type="style" name="Base.Widget.AppCompat.SeekBar" id="0x7f1200f8" />
    <public type="style" name="Base.Widget.AppCompat.Spinner" id="0x7f1200fa" />
    <public type="style" name="Base.Widget.AppCompat.TextView" id="0x7f1200fc" />
    <public type="style" name="Base.Widget.AppCompat.TextView.SpinnerItem" id="0x7f1200fd" />
    <public type="style" name="Base.Widget.AppCompat.Toolbar" id="0x7f1200fe" />
    <public type="style" name="Base.Widget.AppCompat.Toolbar.Button.Navigation" id="0x7f1200ff" />
    <public type="style" name="Base.Widget.Design.TabLayout" id="0x7f120100" />
    <public type="style" name="Base.Widget.MaterialComponents.AutoCompleteTextView" id="0x7f120115" />
    <public type="style" name="Base.Widget.MaterialComponents.CheckedTextView" id="0x7f120116" />
    <public type="style" name="Base.Widget.MaterialComponents.Chip" id="0x7f120117" />
    <public type="style" name="Base.Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton" id="0x7f120118" />
    <public type="style" name="Base.Widget.MaterialComponents.MaterialCalendar.NavigationButton" id="0x7f120119" />
    <public type="style" name="Base.Widget.MaterialComponents.PopupMenu" id="0x7f12011a" />
    <public type="style" name="Base.Widget.MaterialComponents.PopupMenu.ContextMenu" id="0x7f12011b" />
    <public type="style" name="Base.Widget.MaterialComponents.PopupMenu.ListPopupWindow" id="0x7f12011c" />
    <public type="style" name="Base.Widget.MaterialComponents.PopupMenu.Overflow" id="0x7f12011d" />
    <public type="style" name="Base.Widget.MaterialComponents.Slider" id="0x7f12011e" />
    <public type="style" name="Base.Widget.MaterialComponents.Snackbar" id="0x7f12011f" />
    <public type="style" name="Base.Widget.MaterialComponents.TextInputEditText" id="0x7f120120" />
    <public type="style" name="Base.Widget.MaterialComponents.TextInputLayout" id="0x7f120121" />
    <public type="style" name="Base.Widget.MaterialComponents.TextView" id="0x7f120122" />
    <public type="style" name="Base.v21.Theme.SplashScreen" id="0x7f120123" />
    <public type="style" name="Base.v21.Theme.SplashScreen.Light" id="0x7f120124" />
    <public type="style" name="Base.v27.Theme.SplashScreen" id="0x7f120125" />
    <public type="style" name="Base.v27.Theme.SplashScreen.Light" id="0x7f120126" />
    <public type="style" name="Button1Style" id="0x7f120127" />
    <public type="style" name="Button2Style" id="0x7f120128" />
    <public type="style" name="Button3Style" id="0x7f120129" />
    <public type="style" name="ButtonStyleDownload" id="0x7f12012a" />
    <public type="style" name="CardView" id="0x7f12012b" />
    <public type="style" name="DatePickerStyle" id="0x7f12012e" />
    <public type="style" name="DialogWindowTheme" id="0x7f12012f" />
    <public type="style" name="FloatingDialogTheme" id="0x7f120130" />
    <public type="style" name="FloatingDialogWindowTheme" id="0x7f120131" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents" id="0x7f12013c" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents.Body.Text" id="0x7f12013d" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents.Title.Icon" id="0x7f120140" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents.Title.Panel" id="0x7f120142" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents.Title.Text" id="0x7f120144" />
    <public type="style" name="Platform.AppCompat" id="0x7f120146" />
    <public type="style" name="Platform.AppCompat.Light" id="0x7f120147" />
    <public type="style" name="Platform.MaterialComponents" id="0x7f120148" />
    <public type="style" name="Platform.MaterialComponents.Dialog" id="0x7f120149" />
    <public type="style" name="Platform.MaterialComponents.Light" id="0x7f12014a" />
    <public type="style" name="Platform.MaterialComponents.Light.Dialog" id="0x7f12014b" />
    <public type="style" name="Platform.ThemeOverlay.AppCompat" id="0x7f12014c" />
    <public type="style" name="Platform.ThemeOverlay.AppCompat.Dark" id="0x7f12014d" />
    <public type="style" name="Platform.ThemeOverlay.AppCompat.Light" id="0x7f12014e" />
    <public type="style" name="Platform.V21.AppCompat" id="0x7f12014f" />
    <public type="style" name="Platform.V21.AppCompat.Light" id="0x7f120150" />
    <public type="style" name="Platform.V25.AppCompat" id="0x7f120151" />
    <public type="style" name="Platform.V25.AppCompat.Light" id="0x7f120152" />
    <public type="style" name="RtlOverlay.DialogWindowTitle.AppCompat" id="0x7f120154" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.ActionBar.TitleItem" id="0x7f120155" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.DialogTitle.Icon" id="0x7f120156" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem" id="0x7f120157" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup" id="0x7f120158" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut" id="0x7f120159" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow" id="0x7f12015a" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Text" id="0x7f12015b" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Title" id="0x7f12015c" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.Search.DropDown" id="0x7f12015d" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1" id="0x7f12015e" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2" id="0x7f12015f" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.Search.DropDown.Query" id="0x7f120160" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.Search.DropDown.Text" id="0x7f120161" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.SearchView.MagIcon" id="0x7f120162" />
    <public type="style" name="RtlUnderlay.Widget.AppCompat.ActionButton" id="0x7f120163" />
    <public type="style" name="RtlUnderlay.Widget.AppCompat.ActionButton.Overflow" id="0x7f120164" />
    <public type="style" name="ShapeAppearance.M3.Comp.Sheet.Side.Docked.Container.Shape" id="0x7f120172" />
    <public type="style" name="ShapeAppearance.M3.Sys.Shape.Corner.Full" id="0x7f120179" />
    <public type="style" name="ShapeAppearance.M3.Sys.Shape.Corner.None" id="0x7f12017c" />
    <public type="style" name="ShapeAppearance.MaterialComponents" id="0x7f12018a" />
    <public type="style" name="ShapeAppearance.MaterialComponents.Badge" id="0x7f12018b" />
    <public type="style" name="ShapeAppearance.MaterialComponents.LargeComponent" id="0x7f12018c" />
    <public type="style" name="ShapeAppearance.MaterialComponents.MediumComponent" id="0x7f12018d" />
    <public type="style" name="ShapeAppearance.MaterialComponents.SmallComponent" id="0x7f12018e" />
    <public type="style" name="ShapeAppearance.MaterialComponents.Tooltip" id="0x7f12018f" />
    <public type="style" name="ShapeAppearanceOverlay.MaterialComponents.BottomSheet" id="0x7f12019b" />
    <public type="style" name="ShapeAppearanceOverlay.MaterialComponents.Chip" id="0x7f12019c" />
    <public type="style" name="ShapeAppearanceOverlay.MaterialComponents.ExtendedFloatingActionButton" id="0x7f12019d" />
    <public type="style" name="ShapeAppearanceOverlay.MaterialComponents.FloatingActionButton" id="0x7f12019e" />
    <public type="style" name="ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day" id="0x7f12019f" />
    <public type="style" name="ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Window.Fullscreen" id="0x7f1201a0" />
    <public type="style" name="ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Year" id="0x7f1201a1" />
    <public type="style" name="ShapeAppearanceOverlay.MaterialComponents.TextInputLayout.FilledBox" id="0x7f1201a2" />
    <public type="style" name="SplashScreen" id="0x7f1201a3" />
    <public type="style" name="SplashScreenAnimation" id="0x7f1201a4" />
    <public type="style" name="SplashTheme" id="0x7f1201a5" />
    <public type="style" name="TextAppearance.AppCompat" id="0x7f1201a6" />
    <public type="style" name="TextAppearance.AppCompat.Body1" id="0x7f1201a7" />
    <public type="style" name="TextAppearance.AppCompat.Body2" id="0x7f1201a8" />
    <public type="style" name="TextAppearance.AppCompat.Button" id="0x7f1201a9" />
    <public type="style" name="TextAppearance.AppCompat.Caption" id="0x7f1201aa" />
    <public type="style" name="TextAppearance.AppCompat.Display1" id="0x7f1201ab" />
    <public type="style" name="TextAppearance.AppCompat.Display2" id="0x7f1201ac" />
    <public type="style" name="TextAppearance.AppCompat.Display3" id="0x7f1201ad" />
    <public type="style" name="TextAppearance.AppCompat.Display4" id="0x7f1201ae" />
    <public type="style" name="TextAppearance.AppCompat.Headline" id="0x7f1201af" />
    <public type="style" name="TextAppearance.AppCompat.Inverse" id="0x7f1201b0" />
    <public type="style" name="TextAppearance.AppCompat.Large" id="0x7f1201b1" />
    <public type="style" name="TextAppearance.AppCompat.Large.Inverse" id="0x7f1201b2" />
    <public type="style" name="TextAppearance.AppCompat.Light.SearchResult.Subtitle" id="0x7f1201b3" />
    <public type="style" name="TextAppearance.AppCompat.Light.SearchResult.Title" id="0x7f1201b4" />
    <public type="style" name="TextAppearance.AppCompat.Light.Widget.PopupMenu.Large" id="0x7f1201b5" />
    <public type="style" name="TextAppearance.AppCompat.Light.Widget.PopupMenu.Small" id="0x7f1201b6" />
    <public type="style" name="TextAppearance.AppCompat.Medium" id="0x7f1201b7" />
    <public type="style" name="TextAppearance.AppCompat.Medium.Inverse" id="0x7f1201b8" />
    <public type="style" name="TextAppearance.AppCompat.Menu" id="0x7f1201b9" />
    <public type="style" name="TextAppearance.AppCompat.SearchResult.Subtitle" id="0x7f1201ba" />
    <public type="style" name="TextAppearance.AppCompat.SearchResult.Title" id="0x7f1201bb" />
    <public type="style" name="TextAppearance.AppCompat.Small" id="0x7f1201bc" />
    <public type="style" name="TextAppearance.AppCompat.Small.Inverse" id="0x7f1201bd" />
    <public type="style" name="TextAppearance.AppCompat.Subhead" id="0x7f1201be" />
    <public type="style" name="TextAppearance.AppCompat.Subhead.Inverse" id="0x7f1201bf" />
    <public type="style" name="TextAppearance.AppCompat.Title" id="0x7f1201c0" />
    <public type="style" name="TextAppearance.AppCompat.Title.Inverse" id="0x7f1201c1" />
    <public type="style" name="TextAppearance.AppCompat.Tooltip" id="0x7f1201c2" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionBar.Menu" id="0x7f1201c3" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionBar.Subtitle" id="0x7f1201c4" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" id="0x7f1201c5" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionBar.Title" id="0x7f1201c6" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" id="0x7f1201c7" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionMode.Subtitle" id="0x7f1201c8" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse" id="0x7f1201c9" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionMode.Title" id="0x7f1201ca" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse" id="0x7f1201cb" />
    <public type="style" name="TextAppearance.AppCompat.Widget.Button" id="0x7f1201cc" />
    <public type="style" name="TextAppearance.AppCompat.Widget.Button.Borderless.Colored" id="0x7f1201cd" />
    <public type="style" name="TextAppearance.AppCompat.Widget.Button.Colored" id="0x7f1201ce" />
    <public type="style" name="TextAppearance.AppCompat.Widget.Button.Inverse" id="0x7f1201cf" />
    <public type="style" name="TextAppearance.AppCompat.Widget.DropDownItem" id="0x7f1201d0" />
    <public type="style" name="TextAppearance.AppCompat.Widget.PopupMenu.Header" id="0x7f1201d1" />
    <public type="style" name="TextAppearance.AppCompat.Widget.PopupMenu.Large" id="0x7f1201d2" />
    <public type="style" name="TextAppearance.AppCompat.Widget.PopupMenu.Small" id="0x7f1201d3" />
    <public type="style" name="TextAppearance.AppCompat.Widget.Switch" id="0x7f1201d4" />
    <public type="style" name="TextAppearance.AppCompat.Widget.TextView.SpinnerItem" id="0x7f1201d5" />
    <public type="style" name="TextAppearance.Compat.Notification" id="0x7f1201d6" />
    <public type="style" name="TextAppearance.Compat.Notification.Info" id="0x7f1201d7" />
    <public type="style" name="TextAppearance.Compat.Notification.Line2" id="0x7f1201d8" />
    <public type="style" name="TextAppearance.Compat.Notification.Time" id="0x7f1201d9" />
    <public type="style" name="TextAppearance.Compat.Notification.Title" id="0x7f1201da" />
    <public type="style" name="TextAppearance.Design.CollapsingToolbar.Expanded" id="0x7f1201db" />
    <public type="style" name="TextAppearance.Design.Counter" id="0x7f1201dc" />
    <public type="style" name="TextAppearance.Design.Counter.Overflow" id="0x7f1201dd" />
    <public type="style" name="TextAppearance.Design.Error" id="0x7f1201de" />
    <public type="style" name="TextAppearance.Design.HelperText" id="0x7f1201df" />
    <public type="style" name="TextAppearance.Design.Hint" id="0x7f1201e0" />
    <public type="style" name="TextAppearance.Design.Placeholder" id="0x7f1201e1" />
    <public type="style" name="TextAppearance.Design.Prefix" id="0x7f1201e2" />
    <public type="style" name="TextAppearance.Design.Snackbar.Message" id="0x7f1201e3" />
    <public type="style" name="TextAppearance.Design.Suffix" id="0x7f1201e4" />
    <public type="style" name="TextAppearance.Design.Tab" id="0x7f1201e5" />
    <public type="style" name="TextAppearance.M3.Sys.Typescale.BodyLarge" id="0x7f1201e6" />
    <public type="style" name="TextAppearance.M3.Sys.Typescale.BodyMedium" id="0x7f1201e7" />
    <public type="style" name="TextAppearance.M3.Sys.Typescale.BodySmall" id="0x7f1201e8" />
    <public type="style" name="TextAppearance.M3.Sys.Typescale.DisplayLarge" id="0x7f1201e9" />
    <public type="style" name="TextAppearance.M3.Sys.Typescale.DisplayMedium" id="0x7f1201ea" />
    <public type="style" name="TextAppearance.M3.Sys.Typescale.DisplaySmall" id="0x7f1201eb" />
    <public type="style" name="TextAppearance.M3.Sys.Typescale.HeadlineLarge" id="0x7f1201ec" />
    <public type="style" name="TextAppearance.M3.Sys.Typescale.HeadlineMedium" id="0x7f1201ed" />
    <public type="style" name="TextAppearance.M3.Sys.Typescale.HeadlineSmall" id="0x7f1201ee" />
    <public type="style" name="TextAppearance.M3.Sys.Typescale.LabelLarge" id="0x7f1201ef" />
    <public type="style" name="TextAppearance.M3.Sys.Typescale.LabelMedium" id="0x7f1201f0" />
    <public type="style" name="TextAppearance.M3.Sys.Typescale.LabelSmall" id="0x7f1201f1" />
    <public type="style" name="TextAppearance.M3.Sys.Typescale.TitleLarge" id="0x7f1201f2" />
    <public type="style" name="TextAppearance.M3.Sys.Typescale.TitleMedium" id="0x7f1201f3" />
    <public type="style" name="TextAppearance.M3.Sys.Typescale.TitleSmall" id="0x7f1201f4" />
    <public type="style" name="TextAppearance.Material3.ActionBar.Subtitle" id="0x7f1201f5" />
    <public type="style" name="TextAppearance.Material3.ActionBar.Title" id="0x7f1201f6" />
    <public type="style" name="TextAppearance.Material3.BodyLarge" id="0x7f1201f7" />
    <public type="style" name="TextAppearance.Material3.BodyMedium" id="0x7f1201f8" />
    <public type="style" name="TextAppearance.Material3.BodySmall" id="0x7f1201f9" />
    <public type="style" name="TextAppearance.Material3.DisplayLarge" id="0x7f1201fa" />
    <public type="style" name="TextAppearance.Material3.DisplayMedium" id="0x7f1201fb" />
    <public type="style" name="TextAppearance.Material3.DisplaySmall" id="0x7f1201fc" />
    <public type="style" name="TextAppearance.Material3.HeadlineLarge" id="0x7f1201fd" />
    <public type="style" name="TextAppearance.Material3.HeadlineMedium" id="0x7f1201fe" />
    <public type="style" name="TextAppearance.Material3.HeadlineSmall" id="0x7f1201ff" />
    <public type="style" name="TextAppearance.Material3.LabelLarge" id="0x7f120200" />
    <public type="style" name="TextAppearance.Material3.LabelMedium" id="0x7f120201" />
    <public type="style" name="TextAppearance.Material3.LabelSmall" id="0x7f120202" />
    <public type="style" name="TextAppearance.Material3.MaterialTimePicker.Title" id="0x7f120203" />
    <public type="style" name="TextAppearance.Material3.SearchBar" id="0x7f120204" />
    <public type="style" name="TextAppearance.Material3.SearchView" id="0x7f120205" />
    <public type="style" name="TextAppearance.Material3.SearchView.Prefix" id="0x7f120206" />
    <public type="style" name="TextAppearance.Material3.TitleLarge" id="0x7f120207" />
    <public type="style" name="TextAppearance.Material3.TitleMedium" id="0x7f120208" />
    <public type="style" name="TextAppearance.Material3.TitleSmall" id="0x7f120209" />
    <public type="style" name="TextAppearance.MaterialComponents.Badge" id="0x7f12020a" />
    <public type="style" name="TextAppearance.MaterialComponents.Body1" id="0x7f12020b" />
    <public type="style" name="TextAppearance.MaterialComponents.Body2" id="0x7f12020c" />
    <public type="style" name="TextAppearance.MaterialComponents.Button" id="0x7f12020d" />
    <public type="style" name="TextAppearance.MaterialComponents.Caption" id="0x7f12020e" />
    <public type="style" name="TextAppearance.MaterialComponents.Chip" id="0x7f12020f" />
    <public type="style" name="TextAppearance.MaterialComponents.Headline1" id="0x7f120210" />
    <public type="style" name="TextAppearance.MaterialComponents.Headline2" id="0x7f120211" />
    <public type="style" name="TextAppearance.MaterialComponents.Headline3" id="0x7f120212" />
    <public type="style" name="TextAppearance.MaterialComponents.Headline4" id="0x7f120213" />
    <public type="style" name="TextAppearance.MaterialComponents.Headline5" id="0x7f120214" />
    <public type="style" name="TextAppearance.MaterialComponents.Headline6" id="0x7f120215" />
    <public type="style" name="TextAppearance.MaterialComponents.Overline" id="0x7f120216" />
    <public type="style" name="TextAppearance.MaterialComponents.Subtitle1" id="0x7f120217" />
    <public type="style" name="TextAppearance.MaterialComponents.Subtitle2" id="0x7f120218" />
    <public type="style" name="TextAppearance.MaterialComponents.TimePicker.Title" id="0x7f120219" />
    <public type="style" name="TextAppearance.MaterialComponents.Tooltip" id="0x7f12021a" />
    <public type="style" name="TextAppearance.Widget.AppCompat.ExpandedMenu.Item" id="0x7f12021b" />
    <public type="style" name="TextAppearance.Widget.AppCompat.Toolbar.Subtitle" id="0x7f12021c" />
    <public type="style" name="TextAppearance.Widget.AppCompat.Toolbar.Title" id="0x7f12021d" />
    <public type="style" name="Theme.App.Starting" id="0x7f12021e" />
    <public type="style" name="Theme.AppCompat" id="0x7f12021f" />
    <public type="style" name="Theme.AppCompat.CompactMenu" id="0x7f120220" />
    <public type="style" name="Theme.AppCompat.DayNight" id="0x7f120221" />
    <public type="style" name="Theme.AppCompat.DayNight.DarkActionBar" id="0x7f120222" />
    <public type="style" name="Theme.AppCompat.DayNight.Dialog" id="0x7f120223" />
    <public type="style" name="Theme.AppCompat.DayNight.Dialog.Alert" id="0x7f120224" />
    <public type="style" name="Theme.AppCompat.DayNight.Dialog.MinWidth" id="0x7f120225" />
    <public type="style" name="Theme.AppCompat.DayNight.DialogWhenLarge" id="0x7f120226" />
    <public type="style" name="Theme.AppCompat.DayNight.NoActionBar" id="0x7f120227" />
    <public type="style" name="Theme.AppCompat.Dialog" id="0x7f120228" />
    <public type="style" name="Theme.AppCompat.Dialog.Alert" id="0x7f120229" />
    <public type="style" name="Theme.AppCompat.Dialog.MinWidth" id="0x7f12022a" />
    <public type="style" name="Theme.AppCompat.DialogWhenLarge" id="0x7f12022b" />
    <public type="style" name="Theme.AppCompat.Empty" id="0x7f12022c" />
    <public type="style" name="Theme.AppCompat.Light" id="0x7f12022d" />
    <public type="style" name="Theme.AppCompat.Light.DarkActionBar" id="0x7f12022e" />
    <public type="style" name="Theme.AppCompat.Light.Dialog" id="0x7f12022f" />
    <public type="style" name="Theme.AppCompat.Light.Dialog.Alert" id="0x7f120230" />
    <public type="style" name="Theme.AppCompat.Light.Dialog.MinWidth" id="0x7f120231" />
    <public type="style" name="Theme.AppCompat.Light.DialogWhenLarge" id="0x7f120232" />
    <public type="style" name="Theme.AppCompat.Light.NoActionBar" id="0x7f120233" />
    <public type="style" name="Theme.AppCompat.NoActionBar" id="0x7f120234" />
    <public type="style" name="Theme.ConnectSdk" id="0x7f120236" />
    <public type="style" name="Theme.MaterialComponents" id="0x7f12025b" />
    <public type="style" name="Theme.MaterialComponents.BottomSheetDialog" id="0x7f12025c" />
    <public type="style" name="Theme.MaterialComponents.Bridge" id="0x7f12025d" />
    <public type="style" name="Theme.MaterialComponents.CompactMenu" id="0x7f12025e" />
    <public type="style" name="Theme.MaterialComponents.DayNight" id="0x7f12025f" />
    <public type="style" name="Theme.MaterialComponents.DayNight.BottomSheetDialog" id="0x7f120260" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Bridge" id="0x7f120261" />
    <public type="style" name="Theme.MaterialComponents.DayNight.DarkActionBar" id="0x7f120262" />
    <public type="style" name="Theme.MaterialComponents.DayNight.DarkActionBar.Bridge" id="0x7f120263" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Dialog" id="0x7f120264" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Dialog.Alert" id="0x7f120265" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Dialog.Alert.Bridge" id="0x7f120266" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Dialog.Bridge" id="0x7f120267" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Dialog.FixedSize" id="0x7f120268" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Dialog.FixedSize.Bridge" id="0x7f120269" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Dialog.MinWidth" id="0x7f12026a" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Dialog.MinWidth.Bridge" id="0x7f12026b" />
    <public type="style" name="Theme.MaterialComponents.DayNight.DialogWhenLarge" id="0x7f12026c" />
    <public type="style" name="Theme.MaterialComponents.DayNight.NoActionBar" id="0x7f12026d" />
    <public type="style" name="Theme.MaterialComponents.DayNight.NoActionBar.Bridge" id="0x7f12026e" />
    <public type="style" name="Theme.MaterialComponents.Dialog" id="0x7f12026f" />
    <public type="style" name="Theme.MaterialComponents.Dialog.Alert" id="0x7f120270" />
    <public type="style" name="Theme.MaterialComponents.Dialog.Alert.Bridge" id="0x7f120271" />
    <public type="style" name="Theme.MaterialComponents.Dialog.Bridge" id="0x7f120272" />
    <public type="style" name="Theme.MaterialComponents.Dialog.FixedSize" id="0x7f120273" />
    <public type="style" name="Theme.MaterialComponents.Dialog.FixedSize.Bridge" id="0x7f120274" />
    <public type="style" name="Theme.MaterialComponents.Dialog.MinWidth" id="0x7f120275" />
    <public type="style" name="Theme.MaterialComponents.Dialog.MinWidth.Bridge" id="0x7f120276" />
    <public type="style" name="Theme.MaterialComponents.DialogWhenLarge" id="0x7f120277" />
    <public type="style" name="Theme.MaterialComponents.Light" id="0x7f120278" />
    <public type="style" name="Theme.MaterialComponents.Light.BottomSheetDialog" id="0x7f120279" />
    <public type="style" name="Theme.MaterialComponents.Light.Bridge" id="0x7f12027a" />
    <public type="style" name="Theme.MaterialComponents.Light.DarkActionBar" id="0x7f12027b" />
    <public type="style" name="Theme.MaterialComponents.Light.DarkActionBar.Bridge" id="0x7f12027c" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog" id="0x7f12027d" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog.Alert" id="0x7f12027e" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog.Alert.Bridge" id="0x7f12027f" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog.Bridge" id="0x7f120280" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog.FixedSize" id="0x7f120281" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog.FixedSize.Bridge" id="0x7f120282" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog.MinWidth" id="0x7f120283" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog.MinWidth.Bridge" id="0x7f120284" />
    <public type="style" name="Theme.MaterialComponents.Light.DialogWhenLarge" id="0x7f120285" />
    <public type="style" name="Theme.MaterialComponents.Light.NoActionBar" id="0x7f120286" />
    <public type="style" name="Theme.MaterialComponents.Light.NoActionBar.Bridge" id="0x7f120287" />
    <public type="style" name="Theme.MaterialComponents.NoActionBar" id="0x7f120288" />
    <public type="style" name="Theme.MaterialComponents.NoActionBar.Bridge" id="0x7f120289" />
    <public type="style" name="Theme.SplashScreen" id="0x7f12028a" />
    <public type="style" name="Theme.SplashScreen.Common" id="0x7f12028b" />
    <public type="style" name="ThemeActionBar" id="0x7f12028d" />
    <public type="style" name="ThemeOverlay.AppCompat.ActionBar" id="0x7f12028f" />
    <public type="style" name="ThemeOverlay.AppCompat.Dark" id="0x7f120290" />
    <public type="style" name="ThemeOverlay.AppCompat.Dark.ActionBar" id="0x7f120291" />
    <public type="style" name="ThemeOverlay.AppCompat.Dialog" id="0x7f120294" />
    <public type="style" name="ThemeOverlay.AppCompat.Dialog.Alert" id="0x7f120295" />
    <public type="style" name="ThemeOverlay.AppCompat.Light" id="0x7f120296" />
    <public type="style" name="ThemeOverlay.Design.TextInputEditText" id="0x7f120297" />
    <public type="style" name="ThemeOverlay.MaterialComponents.ActionBar.Surface" id="0x7f1202db" />
    <public type="style" name="ThemeOverlay.MaterialComponents.AutoCompleteTextView" id="0x7f1202dc" />
    <public type="style" name="ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox" id="0x7f1202dd" />
    <public type="style" name="ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox" id="0x7f1202df" />
    <public type="style" name="ThemeOverlay.MaterialComponents.BottomSheetDialog" id="0x7f1202e3" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Dark" id="0x7f1202e4" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Dark.ActionBar" id="0x7f1202e5" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Dialog" id="0x7f1202e7" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Dialog.Alert" id="0x7f1202e8" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Dialog.Alert.Framework" id="0x7f1202e9" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Light" id="0x7f1202ea" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework" id="0x7f1202eb" />
    <public type="style" name="ThemeOverlay.MaterialComponents.MaterialAlertDialog" id="0x7f1202ec" />
    <public type="style" name="ThemeOverlay.MaterialComponents.MaterialCalendar" id="0x7f1202f3" />
    <public type="style" name="ThemeOverlay.MaterialComponents.MaterialCalendar.Fullscreen" id="0x7f1202f4" />
    <public type="style" name="ThemeOverlay.MaterialComponents.TextInputEditText" id="0x7f1202f5" />
    <public type="style" name="ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox" id="0x7f1202f6" />
    <public type="style" name="ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox.Dense" id="0x7f1202f7" />
    <public type="style" name="ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox" id="0x7f1202f8" />
    <public type="style" name="ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox.Dense" id="0x7f1202f9" />
    <public type="style" name="ThemeOverlay.MaterialComponents.TimePicker" id="0x7f1202fa" />
    <public type="style" name="ThemeOverlay.MaterialComponents.TimePicker.Display" id="0x7f1202fb" />
    <public type="style" name="ThemeOverlay.MaterialComponents.TimePicker.Display.TextInputEditText" id="0x7f1202fc" />
    <public type="style" name="Widget.AppCompat.ActionBar.Solid" id="0x7f120301" />
    <public type="style" name="Widget.AppCompat.ActionBar.TabBar" id="0x7f120302" />
    <public type="style" name="Widget.AppCompat.ActionBar.TabText" id="0x7f120303" />
    <public type="style" name="Widget.AppCompat.ActionBar.TabView" id="0x7f120304" />
    <public type="style" name="Widget.AppCompat.ActionButton" id="0x7f120305" />
    <public type="style" name="Widget.AppCompat.ActionButton.CloseMode" id="0x7f120306" />
    <public type="style" name="Widget.AppCompat.ActionButton.Overflow" id="0x7f120307" />
    <public type="style" name="Widget.AppCompat.ActionMode" id="0x7f120308" />
    <public type="style" name="Widget.AppCompat.ActivityChooserView" id="0x7f120309" />
    <public type="style" name="Widget.AppCompat.AutoCompleteTextView" id="0x7f12030a" />
    <public type="style" name="Widget.AppCompat.Button" id="0x7f12030b" />
    <public type="style" name="Widget.AppCompat.Button.Borderless" id="0x7f12030c" />
    <public type="style" name="Widget.AppCompat.Button.Borderless.Colored" id="0x7f12030d" />
    <public type="style" name="Widget.AppCompat.Button.ButtonBar.AlertDialog" id="0x7f12030e" />
    <public type="style" name="Widget.AppCompat.Button.Small" id="0x7f120310" />
    <public type="style" name="Widget.AppCompat.ButtonBar" id="0x7f120311" />
    <public type="style" name="Widget.AppCompat.ButtonBar.AlertDialog" id="0x7f120312" />
    <public type="style" name="Widget.AppCompat.CompoundButton.CheckBox" id="0x7f120313" />
    <public type="style" name="Widget.AppCompat.CompoundButton.RadioButton" id="0x7f120314" />
    <public type="style" name="Widget.AppCompat.CompoundButton.Switch" id="0x7f120315" />
    <public type="style" name="Widget.AppCompat.DrawerArrowToggle" id="0x7f120316" />
    <public type="style" name="Widget.AppCompat.DropDownItem.Spinner" id="0x7f120317" />
    <public type="style" name="Widget.AppCompat.EditText" id="0x7f120318" />
    <public type="style" name="Widget.AppCompat.ImageButton" id="0x7f120319" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.Solid" id="0x7f12031b" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.TabBar" id="0x7f12031d" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.TabText" id="0x7f12031f" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.TabView" id="0x7f120321" />
    <public type="style" name="Widget.AppCompat.Light.ActionButton" id="0x7f120323" />
    <public type="style" name="Widget.AppCompat.Light.ActionButton.Overflow" id="0x7f120325" />
    <public type="style" name="Widget.AppCompat.Light.PopupMenu" id="0x7f12032c" />
    <public type="style" name="Widget.AppCompat.Light.PopupMenu.Overflow" id="0x7f12032d" />
    <public type="style" name="Widget.AppCompat.Light.SearchView" id="0x7f12032e" />
    <public type="style" name="Widget.AppCompat.Light.Spinner.DropDown.ActionBar" id="0x7f12032f" />
    <public type="style" name="Widget.AppCompat.ListMenuView" id="0x7f120330" />
    <public type="style" name="Widget.AppCompat.ListPopupWindow" id="0x7f120331" />
    <public type="style" name="Widget.AppCompat.ListView" id="0x7f120332" />
    <public type="style" name="Widget.AppCompat.ListView.DropDown" id="0x7f120333" />
    <public type="style" name="Widget.AppCompat.ListView.Menu" id="0x7f120334" />
    <public type="style" name="Widget.AppCompat.PopupMenu" id="0x7f120335" />
    <public type="style" name="Widget.AppCompat.PopupMenu.Overflow" id="0x7f120336" />
    <public type="style" name="Widget.AppCompat.RatingBar" id="0x7f12033a" />
    <public type="style" name="Widget.AppCompat.RatingBar.Indicator" id="0x7f12033b" />
    <public type="style" name="Widget.AppCompat.RatingBar.Small" id="0x7f12033c" />
    <public type="style" name="Widget.AppCompat.SearchView" id="0x7f12033d" />
    <public type="style" name="Widget.AppCompat.SearchView.ActionBar" id="0x7f12033e" />
    <public type="style" name="Widget.AppCompat.SeekBar" id="0x7f12033f" />
    <public type="style" name="Widget.AppCompat.Spinner" id="0x7f120341" />
    <public type="style" name="Widget.AppCompat.Spinner.DropDown" id="0x7f120342" />
    <public type="style" name="Widget.AppCompat.Spinner.DropDown.ActionBar" id="0x7f120343" />
    <public type="style" name="Widget.AppCompat.TextView" id="0x7f120345" />
    <public type="style" name="Widget.AppCompat.TextView.SpinnerItem" id="0x7f120346" />
    <public type="style" name="Widget.AppCompat.Toolbar" id="0x7f120347" />
    <public type="style" name="Widget.AppCompat.Toolbar.Button.Navigation" id="0x7f120348" />
    <public type="style" name="Widget.Compat.NotificationActionContainer" id="0x7f120349" />
    <public type="style" name="Widget.Compat.NotificationActionText" id="0x7f12034a" />
    <public type="style" name="Widget.Design.AppBarLayout" id="0x7f12034b" />
    <public type="style" name="Widget.Design.BottomNavigationView" id="0x7f12034c" />
    <public type="style" name="Widget.Design.BottomSheet.Modal" id="0x7f12034d" />
    <public type="style" name="Widget.Design.FloatingActionButton" id="0x7f12034f" />
    <public type="style" name="Widget.Design.NavigationView" id="0x7f120350" />
    <public type="style" name="Widget.Design.ScrimInsetsFrameLayout" id="0x7f120351" />
    <public type="style" name="Widget.Design.Snackbar" id="0x7f120352" />
    <public type="style" name="Widget.Design.TabLayout" id="0x7f120353" />
    <public type="style" name="Widget.Design.TextInputEditText" id="0x7f120354" />
    <public type="style" name="Widget.Design.TextInputLayout" id="0x7f120355" />
    <public type="style" name="Widget.Material3.SideSheet" id="0x7f1203e3" />
    <public type="style" name="Widget.MaterialComponents.ActionBar.Surface" id="0x7f120404" />
    <public type="style" name="Widget.MaterialComponents.ActionMode" id="0x7f120405" />
    <public type="style" name="Widget.MaterialComponents.AppBarLayout.Primary" id="0x7f120406" />
    <public type="style" name="Widget.MaterialComponents.AppBarLayout.Surface" id="0x7f120408" />
    <public type="style" name="Widget.MaterialComponents.AutoCompleteTextView.FilledBox" id="0x7f120409" />
    <public type="style" name="Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox" id="0x7f12040b" />
    <public type="style" name="Widget.MaterialComponents.Badge" id="0x7f12040d" />
    <public type="style" name="Widget.MaterialComponents.BottomAppBar" id="0x7f12040e" />
    <public type="style" name="Widget.MaterialComponents.BottomNavigationView" id="0x7f120411" />
    <public type="style" name="Widget.MaterialComponents.BottomSheet" id="0x7f120414" />
    <public type="style" name="Widget.MaterialComponents.BottomSheet.Modal" id="0x7f120415" />
    <public type="style" name="Widget.MaterialComponents.Button" id="0x7f120416" />
    <public type="style" name="Widget.MaterialComponents.Button.OutlinedButton" id="0x7f120418" />
    <public type="style" name="Widget.MaterialComponents.Button.TextButton" id="0x7f12041a" />
    <public type="style" name="Widget.MaterialComponents.Button.TextButton.Dialog" id="0x7f12041b" />
    <public type="style" name="Widget.MaterialComponents.Button.TextButton.Dialog.Flush" id="0x7f12041c" />
    <public type="style" name="Widget.MaterialComponents.Button.TextButton.Snackbar" id="0x7f12041f" />
    <public type="style" name="Widget.MaterialComponents.Button.UnelevatedButton" id="0x7f120420" />
    <public type="style" name="Widget.MaterialComponents.CardView" id="0x7f120422" />
    <public type="style" name="Widget.MaterialComponents.CheckedTextView" id="0x7f120423" />
    <public type="style" name="Widget.MaterialComponents.Chip.Action" id="0x7f120424" />
    <public type="style" name="Widget.MaterialComponents.Chip.Choice" id="0x7f120425" />
    <public type="style" name="Widget.MaterialComponents.Chip.Entry" id="0x7f120426" />
    <public type="style" name="Widget.MaterialComponents.ChipGroup" id="0x7f120428" />
    <public type="style" name="Widget.MaterialComponents.CircularProgressIndicator" id="0x7f120429" />
    <public type="style" name="Widget.MaterialComponents.CompoundButton.CheckBox" id="0x7f12042e" />
    <public type="style" name="Widget.MaterialComponents.CompoundButton.RadioButton" id="0x7f12042f" />
    <public type="style" name="Widget.MaterialComponents.CompoundButton.Switch" id="0x7f120430" />
    <public type="style" name="Widget.MaterialComponents.ExtendedFloatingActionButton" id="0x7f120431" />
    <public type="style" name="Widget.MaterialComponents.ExtendedFloatingActionButton.Icon" id="0x7f120432" />
    <public type="style" name="Widget.MaterialComponents.FloatingActionButton" id="0x7f120433" />
    <public type="style" name="Widget.MaterialComponents.Light.ActionBar.Solid" id="0x7f120434" />
    <public type="style" name="Widget.MaterialComponents.LinearProgressIndicator" id="0x7f120435" />
    <public type="style" name="Widget.MaterialComponents.MaterialButtonToggleGroup" id="0x7f120436" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar" id="0x7f120437" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Day" id="0x7f120438" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Day.Invalid" id="0x7f120439" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Day.Selected" id="0x7f12043a" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Day.Today" id="0x7f12043b" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.DayOfWeekLabel" id="0x7f12043c" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.DayTextView" id="0x7f12043d" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Fullscreen" id="0x7f12043e" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.HeaderCancelButton" id="0x7f12043f" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.HeaderConfirmButton" id="0x7f120440" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.HeaderDivider" id="0x7f120441" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.HeaderLayout" id="0x7f120442" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.HeaderLayout.Fullscreen" id="0x7f120443" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.HeaderSelection" id="0x7f120444" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.HeaderSelection.Fullscreen" id="0x7f120445" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.HeaderTitle" id="0x7f120446" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton" id="0x7f120447" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Item" id="0x7f120448" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.MonthNavigationButton" id="0x7f120449" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.MonthTextView" id="0x7f12044a" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Year" id="0x7f12044b" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Year.Selected" id="0x7f12044c" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Year.Today" id="0x7f12044d" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.YearNavigationButton" id="0x7f12044e" />
    <public type="style" name="Widget.MaterialComponents.NavigationRailView" id="0x7f120450" />
    <public type="style" name="Widget.MaterialComponents.NavigationView" id="0x7f120455" />
    <public type="style" name="Widget.MaterialComponents.PopupMenu" id="0x7f120456" />
    <public type="style" name="Widget.MaterialComponents.PopupMenu.ContextMenu" id="0x7f120457" />
    <public type="style" name="Widget.MaterialComponents.PopupMenu.ListPopupWindow" id="0x7f120458" />
    <public type="style" name="Widget.MaterialComponents.PopupMenu.Overflow" id="0x7f120459" />
    <public type="style" name="Widget.MaterialComponents.ProgressIndicator" id="0x7f12045a" />
    <public type="style" name="Widget.MaterialComponents.Slider" id="0x7f12045c" />
    <public type="style" name="Widget.MaterialComponents.Snackbar" id="0x7f12045d" />
    <public type="style" name="Widget.MaterialComponents.Snackbar.TextView" id="0x7f12045f" />
    <public type="style" name="Widget.MaterialComponents.TabLayout" id="0x7f120460" />
    <public type="style" name="Widget.MaterialComponents.TextInputEditText.FilledBox" id="0x7f120463" />
    <public type="style" name="Widget.MaterialComponents.TextInputEditText.FilledBox.Dense" id="0x7f120464" />
    <public type="style" name="Widget.MaterialComponents.TextInputEditText.OutlinedBox" id="0x7f120465" />
    <public type="style" name="Widget.MaterialComponents.TextInputEditText.OutlinedBox.Dense" id="0x7f120466" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.FilledBox" id="0x7f120467" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.FilledBox.Dense" id="0x7f120468" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.FilledBox.ExposedDropdownMenu" id="0x7f12046a" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.OutlinedBox" id="0x7f12046b" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense" id="0x7f12046c" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu" id="0x7f12046e" />
    <public type="style" name="Widget.MaterialComponents.TextView" id="0x7f12046f" />
    <public type="style" name="Widget.MaterialComponents.TimePicker.Button" id="0x7f120471" />
    <public type="style" name="Widget.MaterialComponents.TimePicker.Clock" id="0x7f120472" />
    <public type="style" name="Widget.MaterialComponents.TimePicker.Display" id="0x7f120473" />
    <public type="style" name="Widget.MaterialComponents.TimePicker.Display.Divider" id="0x7f120474" />
    <public type="style" name="Widget.MaterialComponents.TimePicker.Display.HelperText" id="0x7f120475" />
    <public type="style" name="Widget.MaterialComponents.TimePicker.Display.TextInputEditText" id="0x7f120476" />
    <public type="style" name="Widget.MaterialComponents.TimePicker.Display.TextInputLayout" id="0x7f120477" />
    <public type="style" name="Widget.MaterialComponents.TimePicker.ImageButton" id="0x7f120478" />
    <public type="style" name="Widget.MaterialComponents.TimePicker.ImageButton.ShapeAppearance" id="0x7f120479" />
    <public type="style" name="Widget.MaterialComponents.Toolbar" id="0x7f12047a" />
    <public type="style" name="Widget.MaterialComponents.Tooltip" id="0x7f12047e" />
    <public type="xml" name="image_share_filepaths" id="0x7f140000" />
    <public type="xml" name="provider_paths" id="0x7f140001" />
</resources>
