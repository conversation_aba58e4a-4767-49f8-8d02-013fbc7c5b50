<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:layout_width="wrap_content" android:layout_height="64.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <RelativeLayout android:id="@id/os_bgimage_notif_bgimage_align_layout" android:paddingLeft="0.0dip" android:paddingRight="-2000.0dip" android:layout_width="wrap_content" android:layout_height="64.0dip" android:layoutDirection="ltr">
        <ImageView android:id="@id/os_bgimage_notif_bgimage" android:layout_width="fill_parent" android:layout_height="64.0dip" android:scaleType="fitStart" android:adjustViewBounds="false" android:cropToPadding="false" />
        <ImageView android:id="@id/os_bgimage_notif_bgimage_right_aligned" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="64.0dip" android:scaleType="fitEnd" android:adjustViewBounds="false" android:cropToPadding="false" />
    </RelativeLayout>
    <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="64.0dip" android:layout_marginLeft="@android:dimen/notification_large_icon_width" android:textDirection="locale">
        <TextView android:textAppearance="@android:style/TextAppearance.StatusBar.EventContent.Title" android:ellipsize="marquee" android:id="@id/os_bgimage_notif_title" android:paddingTop="8.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content" android:text="Medium Text" android:singleLine="true" android:paddingStart="4.0dip" />
        <TextView android:textAppearance="@android:style/TextAppearance.StatusBar.EventContent" android:ellipsize="marquee" android:id="@id/os_bgimage_notif_body" android:fadingEdge="horizontal" android:layout_width="fill_parent" android:layout_height="0.0dip" android:text="Small Text" android:singleLine="true" android:layout_weight="1.0" android:paddingStart="4.0dip" />
    </LinearLayout>
</RelativeLayout>
