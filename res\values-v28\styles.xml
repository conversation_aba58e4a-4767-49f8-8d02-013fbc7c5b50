<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="Base.Theme.AppCompat" parent="@style/Base.V28.Theme.AppCompat" />
    <style name="Base.Theme.AppCompat.Light" parent="@style/Base.V28.Theme.AppCompat.Light" />
    <style name="Base.V14.ThemeOverlay.MaterialComponents.MaterialAlertDialog" parent="@style/ThemeOverlay.AppCompat.Dialog.Alert">
        <item name="android:backgroundDimAmount">@dimen/m3_comp_scrim_container_opacity</item>
        <item name="android:checkedTextViewStyle">@style/Widget.MaterialComponents.CheckedTextView</item>
        <item name="android:dialogCornerRadius">@null</item>
        <item name="alertDialogStyle">@style/MaterialAlertDialog.MaterialComponents</item>
        <item name="buttonBarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
        <item name="buttonBarNegativeButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
        <item name="buttonBarNeutralButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush</item>
        <item name="buttonBarPositiveButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
        <item name="materialAlertDialogBodyTextStyle">@style/MaterialAlertDialog.MaterialComponents.Body.Text</item>
        <item name="materialAlertDialogButtonSpacerVisibility">@integer/mtrl_view_invisible</item>
    </style>
    <style name="TextAppearance.M3.Sys.Typescale.BodyLarge" parent="@style/TextAppearance.AppCompat.Body2">
        <item name="android:textSize">16.0sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@string/m3_ref_typeface_plain_regular</item>
        <item name="android:letterSpacing">0.03125</item>
        <item name="android:lineHeight">24.0sp</item>
        <item name="fontFamily">@string/m3_ref_typeface_plain_regular</item>
        <item name="lineHeight">24.0sp</item>
    </style>
    <style name="TextAppearance.M3.Sys.Typescale.BodyMedium" parent="@style/TextAppearance.AppCompat.Body1">
        <item name="android:textSize">14.0sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@string/m3_ref_typeface_plain_regular</item>
        <item name="android:letterSpacing">0.01785714</item>
        <item name="android:lineHeight">20.0sp</item>
        <item name="fontFamily">@string/m3_ref_typeface_plain_regular</item>
        <item name="lineHeight">20.0sp</item>
    </style>
    <style name="TextAppearance.M3.Sys.Typescale.BodySmall" parent="@style/TextAppearance.AppCompat.Caption">
        <item name="android:textSize">12.0sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@string/m3_ref_typeface_plain_regular</item>
        <item name="android:letterSpacing">0.03333333</item>
        <item name="android:lineHeight">16.0sp</item>
        <item name="fontFamily">@string/m3_ref_typeface_plain_regular</item>
        <item name="lineHeight">16.0sp</item>
    </style>
    <style name="TextAppearance.M3.Sys.Typescale.DisplayLarge" parent="@style/TextAppearance.AppCompat.Display3">
        <item name="android:textSize">57.0sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@string/m3_ref_typeface_brand_regular</item>
        <item name="android:letterSpacing">-0.00438596</item>
        <item name="android:lineHeight">64.0sp</item>
        <item name="fontFamily">@string/m3_ref_typeface_brand_regular</item>
        <item name="lineHeight">64.0sp</item>
    </style>
    <style name="TextAppearance.M3.Sys.Typescale.DisplayMedium" parent="@style/TextAppearance.AppCompat.Display2">
        <item name="android:textSize">45.0sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@string/m3_ref_typeface_brand_regular</item>
        <item name="android:letterSpacing">0.0</item>
        <item name="android:lineHeight">52.0sp</item>
        <item name="fontFamily">@string/m3_ref_typeface_brand_regular</item>
        <item name="lineHeight">52.0sp</item>
    </style>
    <style name="TextAppearance.M3.Sys.Typescale.DisplaySmall" parent="@style/TextAppearance.AppCompat.Display1">
        <item name="android:textSize">36.0sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@string/m3_ref_typeface_brand_regular</item>
        <item name="android:letterSpacing">0.0</item>
        <item name="android:lineHeight">44.0sp</item>
        <item name="fontFamily">@string/m3_ref_typeface_brand_regular</item>
        <item name="lineHeight">44.0sp</item>
    </style>
    <style name="TextAppearance.M3.Sys.Typescale.HeadlineLarge" parent="@style/TextAppearance.AppCompat.Display1">
        <item name="android:textSize">32.0sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@string/m3_ref_typeface_brand_regular</item>
        <item name="android:letterSpacing">0.0</item>
        <item name="android:lineHeight">40.0sp</item>
        <item name="fontFamily">@string/m3_ref_typeface_brand_regular</item>
        <item name="lineHeight">40.0sp</item>
    </style>
    <style name="TextAppearance.M3.Sys.Typescale.HeadlineMedium" parent="@style/TextAppearance.AppCompat.Headline">
        <item name="android:textSize">28.0sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@string/m3_ref_typeface_brand_regular</item>
        <item name="android:letterSpacing">0.0</item>
        <item name="android:lineHeight">36.0sp</item>
        <item name="fontFamily">@string/m3_ref_typeface_brand_regular</item>
        <item name="lineHeight">36.0sp</item>
    </style>
    <style name="TextAppearance.M3.Sys.Typescale.HeadlineSmall" parent="@style/TextAppearance.AppCompat.Title">
        <item name="android:textSize">24.0sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@string/m3_ref_typeface_brand_regular</item>
        <item name="android:letterSpacing">0.0</item>
        <item name="android:lineHeight">32.0sp</item>
        <item name="fontFamily">@string/m3_ref_typeface_brand_regular</item>
        <item name="lineHeight">32.0sp</item>
    </style>
    <style name="TextAppearance.M3.Sys.Typescale.LabelLarge" parent="@style/TextAppearance.AppCompat.Body1">
        <item name="android:textSize">14.0sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@string/m3_ref_typeface_plain_medium</item>
        <item name="android:letterSpacing">0.00714286</item>
        <item name="android:lineHeight">20.0sp</item>
        <item name="fontFamily">@string/m3_ref_typeface_plain_medium</item>
        <item name="lineHeight">20.0sp</item>
    </style>
    <style name="TextAppearance.M3.Sys.Typescale.LabelMedium" parent="@style/TextAppearance.AppCompat.Caption">
        <item name="android:textSize">12.0sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@string/m3_ref_typeface_plain_medium</item>
        <item name="android:letterSpacing">0.04166667</item>
        <item name="android:lineHeight">16.0sp</item>
        <item name="fontFamily">@string/m3_ref_typeface_plain_medium</item>
        <item name="lineHeight">16.0sp</item>
    </style>
    <style name="TextAppearance.M3.Sys.Typescale.LabelSmall" parent="@style/TextAppearance.AppCompat.Caption">
        <item name="android:textSize">11.0sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@string/m3_ref_typeface_plain_medium</item>
        <item name="android:letterSpacing">0.04545455</item>
        <item name="android:lineHeight">16.0sp</item>
        <item name="fontFamily">@string/m3_ref_typeface_plain_medium</item>
        <item name="lineHeight">16.0sp</item>
    </style>
    <style name="TextAppearance.M3.Sys.Typescale.TitleLarge" parent="@style/TextAppearance.AppCompat.Title">
        <item name="android:textSize">22.0sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@string/m3_ref_typeface_brand_regular</item>
        <item name="android:letterSpacing">0.0</item>
        <item name="android:lineHeight">28.0sp</item>
        <item name="fontFamily">@string/m3_ref_typeface_brand_regular</item>
        <item name="lineHeight">28.0sp</item>
    </style>
    <style name="TextAppearance.M3.Sys.Typescale.TitleMedium" parent="@style/TextAppearance.AppCompat.Subhead">
        <item name="android:textSize">16.0sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@string/m3_ref_typeface_plain_medium</item>
        <item name="android:letterSpacing">0.009375</item>
        <item name="android:lineHeight">24.0sp</item>
        <item name="fontFamily">@string/m3_ref_typeface_plain_medium</item>
        <item name="lineHeight">24.0sp</item>
    </style>
    <style name="TextAppearance.M3.Sys.Typescale.TitleSmall" parent="@style/TextAppearance.AppCompat.Subhead">
        <item name="android:textSize">14.0sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@string/m3_ref_typeface_plain_medium</item>
        <item name="android:letterSpacing">0.00714286</item>
        <item name="android:lineHeight">20.0sp</item>
        <item name="fontFamily">@string/m3_ref_typeface_plain_medium</item>
        <item name="lineHeight">20.0sp</item>
    </style>
    <style name="Base.V28.Theme.AppCompat" parent="@style/Base.V26.Theme.AppCompat">
        <item name="dialogCornerRadius">?android:dialogCornerRadius</item>
    </style>
    <style name="Base.V28.Theme.AppCompat.Light" parent="@style/Base.V26.Theme.AppCompat.Light">
        <item name="dialogCornerRadius">?android:dialogCornerRadius</item>
    </style>
</resources>
