{"stickyFooter": {"enable": false}, "biometricAuth": {"enable": false, "showOnLoad": false, "showOnLoadWithSecret": false, "whiteListDomains": []}, "navigationLoader": {}, "regexInternalExternal": {"rules": [{"regex": "^(?!https?://).*", "pageType": "CUSTOM", "label": "Non-web links", "type": "EXTERNAL"}, {"regex": "https?://([-\\w]+\\.)*facebook\\.com.*", "pageType": "CUSTOM", "label": "Facebook", "type": "EXTERNAL"}, {"regex": "https?://([-\\w]+\\.)*twitter\\.com/.*", "pageType": "CUSTOM", "label": "Twitter", "type": "EXTERNAL"}, {"regex": "https?://([-\\w]+\\.)*instagram\\.com/.*", "pageType": "CUSTOM", "label": "Instagram", "type": "EXTERNAL"}, {"regex": "https?://maps\\.google\\.com.*", "pageType": "CUSTOM", "label": "Google Maps", "type": "EXTERNAL"}, {"regex": "https?://([-\\w]+\\.)*google\\.com/maps/search/.*", "pageType": "CUSTOM", "label": "Google Maps Search", "type": "EXTERNAL"}, {"regex": "https?://([-\\w]+\\.)*linkedin\\.com/.*", "pageType": "CUSTOM", "label": "LinkedIn", "type": "EXTERNAL"}, {"regex": "https?://([-\\w]+\\.)*whatsapp\\.com/.*", "pageType": "CUSTOM", "label": "Whatsapp", "type": "EXTERNAL"}, {"regex": ".*", "pageType": "ALL_PAGES", "label": "All Other Links", "type": "INTERNAL"}]}, "noInternetData": {"pages": [{"topMarginPercent": 14, "elements": [{"fileName": "no_internet.png", "fileUrl": "https://firebasestorage.googleapis.com/v0/b/web-to-native-website.appspot.com/o/user-uploads%2FOpoe1n37jTk4eeQGUEGY%2Fuser-upload%2Ficon_ANDROID1729139355652.png?alt=media&token=c5be760f-636b-4f66-83f5-ba56f89dc7bc", "type": "image"}, {"fontSize": 32, "type": "text", "value": "Oops!", "textColor": "#ffffff", "fontWeight": 700}, {"fontSize": 14, "type": "text", "value": "Claro cliente parece que a sua conexão está um pouco lenta recomendo que verifique isso.", "textColor": "#fffa00", "fontWeight": 400}]}], "bgColor": "#0e3b00", "actionButtons": [{"btnBgColor": "#004600", "borderColor": "#00ff07", "borderWidth": 4, "text": "Try Again", "roundedCornerPercent": 8, "textColor": "#ffffff"}]}, "iapScreenData": null, "cssValue": ""}