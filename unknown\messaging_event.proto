// Copyright 2020 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package reporting;

// Describes a Firebase Messaging event on a client.
// LINT.IfChange
message MessagingClientEvent {
  // The project number used to send the message.
  int64 project_number = 1;

  // The message id aka persistent id.
  string message_id = 2;

  // The instance id or fid of the app the message is sent to.
  string instance_id = 3;

  enum MessageType {
    UNKNOWN = 0;
    DATA_MESSAGE = 1;
    TOPIC = 2;
    DISPLAY_NOTIFICATION = 3;
  }

  // The type of the message.
  MessageType message_type = 4;

  enum SDKPlatform {
    UNKNOWN_OS = 0;
    ANDROID = 1;
    IOS = 2;
    WEB = 3;
  }

  // The platform of the recipient.
  SDKPlatform sdk_platform = 5;

  // The package name for Android apps or the bundle id for iOS apps.
  string package_name = 6;

  // The collapse key set for this message.
  string collapse_key = 7;

  // Priority level of the message.
  // 5 = normal, 10 = high
  int32 priority = 8;

  // TTL for the message, if set.
  int32 ttl = 9;

  // The topic the message is sent to.
  string topic = 10;

  // An id generated by the server to group all messages belonging to a single
  // request together.
  int64 bulk_id = 11;

  enum Event {
    UNKNOWN_EVENT = 0;
    MESSAGE_DELIVERED = 1;
    MESSAGE_OPEN = 2;
  }
  // The status for the event being logged.
  Event event = 12;

  // Label provided by developer for analytics purposes.
  string analytics_label = 13;

  // The id of the Firebase notifications campaign
  int64 campaign_id = 14;

  // The name of the Firebase notifications campaign ("Notification name"
  // parameter set in campaign composer in Firebase console).
  string composer_label = 15;
}
// LINT.ThenChange(//depot/google3/logs/proto/firebase/cloud_messaging/client/messaging_event.proto)
